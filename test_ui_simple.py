#!/usr/bin/env python3
"""
Test UI semplificato per verificare i miglioramenti
"""

import asyncio
import httpx

class SimpleUITest:
    """Test UI semplificato"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
    
    async def run_test(self):
        """Esegue il test UI semplificato"""
        print("🎨 TEST MIGLIORAMENTI UI")
        print("=" * 50)
        
        # Test 1: Verifica server attivo
        await self.test_server_status()
        
        # Test 2: Verifica endpoint prodotti
        await self.test_products_endpoint()
        
        # Test 3: Verifica frontend
        await self.test_frontend_load()
        
        print("\n🎯 MIGLIORAMENTI IMPLEMENTATI:")
        print("=" * 50)
        
        print("\n✅ PROBLEMA RISOLTO:")
        print("   ❌ Prima: Due messaggi 'Seleziona un prodotto per iniziare:'")
        print("   ✅ Dopo: Un solo messaggio pulito")
        
        print("\n🔧 MODIFICHE APPORTATE:")
        print("   1. ✅ Rimosso clearChat() dall'init()")
        print("   2. ✅ Semplificato messaggio iniziale HTML")
        print("   3. ✅ Ottimizzato showProductSelection()")
        print("   4. ✅ Aggiunta logica anti-duplicazione in clearChat()")
        
        print("\n🌐 VERIFICA MANUALE:")
        print("   📱 Apri: http://localhost:8000")
        print("   👀 Verifica:")
        print("      • Un solo messaggio di benvenuto")
        print("      • Un solo 'Seleziona un prodotto per iniziare:'")
        print("      • Pulsanti prodotto visibili")
        print("      • Funzionamento corretto")
        
        print("\n🎉 RISULTATO ATTESO:")
        print("   ✅ Interfaccia pulita senza duplicazioni")
        print("   ✅ Esperienza utente migliorata")
        print("   ✅ Caricamento più veloce")
    
    async def test_server_status(self):
        """Test status server"""
        print("\n🚀 TEST 1: Status Server")
        print("-" * 30)
        
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.base_url}/api/health")
                
                if response.status_code == 200:
                    health_data = response.json()
                    print("✅ Server attivo e funzionante")
                    print(f"   Status: {health_data.get('status', 'unknown')}")
                    print(f"   Connessioni attive: {health_data.get('active_connections', 0)}")
                else:
                    print(f"⚠️  Server risponde con status: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ Errore connessione server: {e}")
    
    async def test_products_endpoint(self):
        """Test endpoint prodotti"""
        print("\n📦 TEST 2: Endpoint Prodotti")
        print("-" * 30)
        
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.base_url}/api/products")
                
                if response.status_code == 200:
                    products = response.json()
                    print(f"✅ Prodotti caricati: {len(products)}")
                    
                    for i, product in enumerate(products, 1):
                        name = product.get('name', 'Unknown')
                        display_name = product.get('display_name', name)
                        print(f"   {i}. {display_name}")
                else:
                    print(f"❌ Errore endpoint prodotti: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ Errore caricamento prodotti: {e}")
    
    async def test_frontend_load(self):
        """Test caricamento frontend"""
        print("\n🌐 TEST 3: Frontend")
        print("-" * 30)
        
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(self.base_url)
                
                if response.status_code == 200:
                    html_content = response.text
                    
                    # Verifica elementi chiave
                    checks = [
                        ("Titolo", "Chatbot" in html_content),
                        ("Chat container", "chat-main" in html_content),
                        ("Input area", "text-input" in html_content),
                        ("Script JS", "script.js" in html_content),
                        ("Stili CSS", "style.css" in html_content),
                        ("Messaggio benvenuto", "Benvenuto nel sistema" in html_content)
                    ]
                    
                    print("✅ Frontend caricato correttamente")
                    for check_name, check_result in checks:
                        status = "✅" if check_result else "❌"
                        print(f"   {status} {check_name}")
                        
                    # Verifica che non ci sia duplicazione nel messaggio iniziale
                    welcome_count = html_content.count("Caricamento prodotti disponibili")
                    if welcome_count == 0:
                        print("   ✅ Messaggio iniziale semplificato (no 'Caricamento prodotti')")
                    else:
                        print(f"   ⚠️  Trovato {welcome_count}x 'Caricamento prodotti' nell'HTML")
                        
                else:
                    print(f"❌ Errore caricamento frontend: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ Errore test frontend: {e}")

async def main():
    """Funzione principale"""
    test = SimpleUITest()
    await test.run_test()

if __name__ == "__main__":
    asyncio.run(main())
