#!/usr/bin/env python3
"""
Debug script to analyze document extraction and search functionality
"""

import asyncio
import logging
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from document_processor import DocumentProcessor
from mcp_server import MCPResource

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_document_extraction():
    """Debug document extraction for specific files"""
    
    # Initialize document processor
    doc_processor = DocumentProcessor()
    
    # Define the document we want to analyze
    manuale_officina_path = Path("sorgenti/SYM/link/Manuale officina Symphony ST 200 E5.pdf")
    
    if not manuale_officina_path.exists():
        logger.error(f"Document not found: {manuale_officina_path}")
        return
    
    # Create MCPResource
    resource = MCPResource(
        name="SYM/link/Manuale officina Symphony ST 200 E5",
        uri=f"file://{manuale_officina_path.absolute()}",
        description="Technical document for SYM",
        metadata={
            'file_path': str(manuale_officina_path.absolute()),
            'product': 'SYM',
            'filename': 'Manuale officina Symphony ST 200 E5.pdf',
            'citation_required': True
        }
    )
    
    # Process the document
    logger.info("Processing document...")
    processed_doc = await doc_processor.process_resource(resource)
    
    if not processed_doc:
        logger.error("Failed to process document")
        return
    
    logger.info(f"Document processed successfully:")
    logger.info(f"- Page count: {processed_doc.page_count}")
    logger.info(f"- Chunk count: {len(processed_doc.chunks)}")
    logger.info(f"- Table count: {len(processed_doc.tables)}")
    logger.info(f"- Image count: {len(processed_doc.images)}")
    
    # Analyze specific pages
    print("\n" + "="*80)
    print("ANALYZING PAGE 4 (should contain table)")
    print("="*80)
    
    page_4_chunks = [chunk for chunk in processed_doc.chunks if chunk.page_number == 4]
    for i, chunk in enumerate(page_4_chunks):
        print(f"\n--- Page 4, Chunk {i+1} ---")
        print(chunk.text[:500] + "..." if len(chunk.text) > 500 else chunk.text)
    
    print("\n" + "="*80)
    print("ANALYZING PAGE 9 (should contain bilanciere info)")
    print("="*80)
    
    page_9_chunks = [chunk for chunk in processed_doc.chunks if chunk.page_number == 9]
    for i, chunk in enumerate(page_9_chunks):
        print(f"\n--- Page 9, Chunk {i+1} ---")
        print(chunk.text[:500] + "..." if len(chunk.text) > 500 else chunk.text)
    
    # Search for specific terms
    print("\n" + "="*80)
    print("TESTING SEARCH FUNCTIONALITY")
    print("="*80)
    
    search_terms = [
        "bilanciere",
        "diametro interno",
        "diametro interno del bilanciere",
        "tabella",
        "specifiche tecniche"
    ]
    
    for term in search_terms:
        print(f"\n--- Searching for: '{term}' ---")
        results = await doc_processor.search_documents(term, "SYM")
        
        if results:
            for i, result in enumerate(results[:3]):  # Show top 3 results
                print(f"Result {i+1} (score: {result['score']:.3f}, page: {result['page']}):")
                content = result['content'][:200] + "..." if len(result['content']) > 200 else result['content']
                print(f"  {content}")
        else:
            print("  No results found")
    
    # Analyze tables
    print("\n" + "="*80)
    print("ANALYZING EXTRACTED TABLES")
    print("="*80)
    
    for i, table in enumerate(processed_doc.tables):
        print(f"\n--- Table {i+1} (Page {table['page']}) ---")
        print(table['content'][:300] + "..." if len(table['content']) > 300 else table['content'])
    
    # Look for bilanciere in full text
    print("\n" + "="*80)
    print("SEARCHING 'BILANCIERE' IN FULL TEXT")
    print("="*80)
    
    full_text = processed_doc.full_text.lower()
    if 'bilanciere' in full_text:
        # Find all occurrences
        import re
        matches = []
        for match in re.finditer(r'.{0,100}bilanciere.{0,100}', full_text, re.IGNORECASE):
            matches.append(match.group())
        
        print(f"Found {len(matches)} occurrences of 'bilanciere':")
        for i, match in enumerate(matches[:5]):  # Show first 5
            print(f"{i+1}: ...{match}...")
    else:
        print("'bilanciere' not found in full text")

if __name__ == "__main__":
    asyncio.run(debug_document_extraction())
