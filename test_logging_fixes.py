#!/usr/bin/env python3
"""
Test script to verify the logging fixes
Tests IP address, search_result_count, and avg_relevance_score logging
"""

import asyncio
import logging
import sys
from datetime import datetime
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from database_logger import get_database_logger, close_database_logger
from session_manager import SessionManager
from mcp_server import MCPServer
from query_engine import QueryResult

# Setup logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_ip_address_logging():
    """Test IP address logging for both localhost and external IPs"""
    print("🔍 Testing IP address logging...")
    
    try:
        # Initialize components
        mcp_server = MCPServer()
        await mcp_server.start()
        session_manager = SessionManager(mcp_server)
        
        # Test localhost IP
        print("  Testing localhost IP...")
        result = await session_manager.process_query_with_ip(
            "Test query for localhost",
            "TestProduct",
            "127.0.0.1"
        )
        
        # Test external IP
        print("  Testing external IP...")
        result = await session_manager.process_query_with_ip(
            "Test query for external IP",
            "TestProduct", 
            "*************"
        )
        
        # Check database
        db_logger = await get_database_logger()
        recent_conversations = await db_logger.get_recent_conversations(5)
        
        print("  Recent conversations with IP addresses:")
        for conv in recent_conversations:
            ip = conv.get('ip_address', 'NULL')
            question = conv.get('question', '')[:50]
            print(f"    IP: {ip} | Question: {question}...")
        
        await mcp_server.stop()
        print("✅ IP address logging test completed")
        return True
        
    except Exception as e:
        print(f"❌ IP address logging test failed: {e}")
        return False

async def test_search_metrics_logging():
    """Test search result count and relevance score logging"""
    print("🔍 Testing search metrics logging...")
    
    try:
        # Initialize components
        mcp_server = MCPServer()
        await mcp_server.start()
        session_manager = SessionManager(mcp_server)
        
        # Test with a query that should return search results
        print("  Testing query with search results...")
        result = await session_manager.process_query_with_ip(
            "Come funziona il motore?",  # Technical automotive query
            "TestProduct",
            "127.0.0.1"
        )
        
        # Check database for search metrics
        db_logger = await get_database_logger()
        recent_conversations = await db_logger.get_recent_conversations(3)
        
        print("  Recent conversations with search metrics:")
        for conv in recent_conversations:
            search_count = conv.get('search_result_count', 0)
            confidence = conv.get('confidence', 0)
            used_general = conv.get('used_general_knowledge', False)
            question = conv.get('question', '')[:40]
            print(f"    Question: {question}...")
            print(f"    Search results: {search_count}")
            print(f"    Confidence: {confidence}")
            print(f"    Used general knowledge: {used_general}")
            print()
        
        await mcp_server.stop()
        print("✅ Search metrics logging test completed")
        return True
        
    except Exception as e:
        print(f"❌ Search metrics logging test failed: {e}")
        return False

async def test_context_info_extraction():
    """Test that context_info is properly extracted from QueryResult"""
    print("🔍 Testing context_info extraction...")
    
    try:
        # Create a mock QueryResult with proper context_info
        query_result = QueryResult(
            answer="Test answer",
            citations=["Test citation"],
            context_info={
                'search_result_count': 5,
                'avg_relevance_score': 0.8500,
                'used_general_knowledge': False
            },
            tool_results=[],
            confidence=0.9200
        )
        
        # Initialize components
        mcp_server = MCPServer()
        await mcp_server.start()
        session_manager = SessionManager(mcp_server)
        
        # Test the logging method directly
        await session_manager._log_conversation_to_database(
            query="Test context info extraction",
            query_result=query_result,
            product="TestProduct",
            response_time_ms=1500,
            ip_address="*************"
        )
        
        # Check database
        db_logger = await get_database_logger()
        recent_conversations = await db_logger.get_recent_conversations(1)
        
        if recent_conversations:
            conv = recent_conversations[0]
            print("  Last logged conversation:")
            print(f"    Search result count: {conv.get('search_result_count', 'NULL')}")
            print(f"    Avg relevance score: {conv.get('avg_relevance_score', 'NULL')}")
            print(f"    Used general knowledge: {conv.get('used_general_knowledge', 'NULL')}")
            print(f"    Confidence: {conv.get('confidence', 'NULL')}")
            print(f"    Response time: {conv.get('response_time_ms', 'NULL')}ms")
            print(f"    IP address: {conv.get('ip_address', 'NULL')}")
            
            # Verify values
            if (conv.get('search_result_count') == 5 and 
                abs(float(conv.get('avg_relevance_score', 0)) - 0.8500) < 0.001 and
                conv.get('used_general_knowledge') == 0 and  # False = 0 in MySQL
                abs(float(conv.get('confidence', 0)) - 0.9200) < 0.001):
                print("✅ Context info extraction working correctly")
                success = True
            else:
                print("❌ Context info values don't match expected values")
                success = False
        else:
            print("❌ No conversations found in database")
            success = False
        
        await mcp_server.stop()
        return success
        
    except Exception as e:
        print(f"❌ Context info extraction test failed: {e}")
        return False

async def test_web_vs_cli_logging():
    """Test that web and CLI logging work differently for IP"""
    print("🔍 Testing web vs CLI logging differences...")
    
    try:
        # Initialize components
        mcp_server = MCPServer()
        await mcp_server.start()
        session_manager = SessionManager(mcp_server)
        
        # Simulate CLI query (should use 127.0.0.1)
        print("  Testing CLI-style query...")
        await session_manager.process_query_with_ip(
            "CLI test query",
            "TestProduct",
            "127.0.0.1"
        )
        
        # Simulate web query (should use provided IP)
        print("  Testing web-style query...")
        await session_manager.process_query_with_ip(
            "Web test query",
            "TestProduct", 
            "************"  # Example external IP
        )
        
        # Check database
        db_logger = await get_database_logger()
        recent_conversations = await db_logger.get_recent_conversations(5)
        
        print("  Recent conversations by source:")
        cli_found = False
        web_found = False
        
        for conv in recent_conversations:
            ip = conv.get('ip_address', 'NULL')
            question = conv.get('question', '')
            
            if 'CLI test' in question and ip == '127.0.0.1':
                print(f"    ✅ CLI: {question[:30]}... | IP: {ip}")
                cli_found = True
            elif 'Web test' in question and ip == '************':
                print(f"    ✅ Web: {question[:30]}... | IP: {ip}")
                web_found = True
        
        success = cli_found and web_found
        if success:
            print("✅ Web vs CLI logging working correctly")
        else:
            print("❌ Web vs CLI logging not working as expected")
        
        await mcp_server.stop()
        return success
        
    except Exception as e:
        print(f"❌ Web vs CLI logging test failed: {e}")
        return False

async def run_logging_fix_tests():
    """Run all logging fix tests"""
    print("🚀 Testing Logging Fixes")
    print("=" * 50)
    
    tests = [
        ("IP Address Logging", test_ip_address_logging),
        ("Search Metrics Logging", test_search_metrics_logging),
        ("Context Info Extraction", test_context_info_extraction),
        ("Web vs CLI Logging", test_web_vs_cli_logging),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 LOGGING FIX TEST RESULTS")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All logging fixes are working correctly!")
        print("\nThe following issues have been resolved:")
        print("✅ IP address now correctly logs 127.0.0.1 for localhost")
        print("✅ search_result_count now extracts from context_info")
        print("✅ avg_relevance_score now extracts from context_info")
        print("✅ Web and CLI logging work with proper IP addresses")
    else:
        print("⚠️ Some logging fixes still need attention.")
    
    # Cleanup
    await close_database_logger()
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(run_logging_fix_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        sys.exit(1)
