#!/usr/bin/env python3
"""
Cache Performance Test Suite
Comprehensive testing of cache effectiveness and performance improvements
"""

import asyncio
import logging
import time
import statistics
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from document_processor import DocumentProcessor
from mcp_server import MCPResource
from cache_service import get_cache_service, start_cache_service, stop_cache_service
from config import config

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CachePerformanceTester:
    """Comprehensive cache performance testing"""
    
    def __init__(self):
        self.test_files = []
        self.results = {}
    
    def find_test_files(self, max_files: int = 5) -> list[Path]:
        """Find PDF files for testing"""
        sorgenti_path = config.sorgenti_path
        pdf_files = list(sorgenti_path.rglob("*.pdf"))
        
        if not pdf_files:
            raise Exception("No PDF files found for testing")
        
        # Select files of different sizes for comprehensive testing
        pdf_files.sort(key=lambda f: f.stat().st_size)
        
        # Take files from different size ranges
        selected = []
        if len(pdf_files) >= max_files:
            step = len(pdf_files) // max_files
            for i in range(0, len(pdf_files), step):
                if len(selected) < max_files:
                    selected.append(pdf_files[i])
        else:
            selected = pdf_files[:max_files]
        
        self.test_files = selected
        return selected
    
    def create_test_resource(self, file_path: Path) -> MCPResource:
        """Create test resource for a file"""
        return MCPResource(
            name=f"test/{file_path.name}",
            uri=f"file://{file_path.absolute()}",
            description="Performance test document",
            metadata={
                'file_path': str(file_path.absolute()),
                'product': 'TEST',
                'filename': file_path.name,
                'citation_required': True
            }
        )
    
    async def test_without_cache(self, file_path: Path, iterations: int = 3) -> dict:
        """Test processing without cache"""
        print(f"🔄 Testing WITHOUT cache: {file_path.name}")
        
        processor = DocumentProcessor(enable_cache=False)
        resource = self.create_test_resource(file_path)
        
        times = []
        results = []
        
        for i in range(iterations):
            start_time = time.time()
            result = await processor.process_resource(resource)
            end_time = time.time()
            
            if result:
                times.append(end_time - start_time)
                results.append(result)
                print(f"   Run {i+1}: {end_time - start_time:.2f}s")
            else:
                print(f"   Run {i+1}: FAILED")
        
        if not times:
            return {'success': False, 'error': 'All runs failed'}
        
        return {
            'success': True,
            'times': times,
            'avg_time': statistics.mean(times),
            'min_time': min(times),
            'max_time': max(times),
            'std_dev': statistics.stdev(times) if len(times) > 1 else 0,
            'chunks': len(results[0].chunks) if results else 0,
            'pages': results[0].page_count if results else 0
        }
    
    async def test_with_cache(self, file_path: Path, iterations: int = 3) -> dict:
        """Test processing with cache (first run = miss, subsequent = hits)"""
        print(f"🔄 Testing WITH cache: {file_path.name}")
        
        # Clear any existing cache for this file
        cache_service = get_cache_service()
        cache_manager = cache_service.get_cache_manager()
        if cache_manager:
            await cache_manager.invalidate_file_cache(file_path)
        
        processor = DocumentProcessor(enable_cache=True)
        resource = self.create_test_resource(file_path)
        
        times = []
        results = []
        cache_hits = []
        
        for i in range(iterations):
            start_time = time.time()
            result = await processor.process_resource(resource)
            end_time = time.time()
            
            if result:
                times.append(end_time - start_time)
                results.append(result)
                # First run is cache miss, subsequent are hits
                cache_hits.append(i > 0)
                status = "HIT" if i > 0 else "MISS"
                print(f"   Run {i+1} ({status}): {end_time - start_time:.2f}s")
            else:
                print(f"   Run {i+1}: FAILED")
        
        if not times:
            return {'success': False, 'error': 'All runs failed'}
        
        # Separate cache miss and hit times
        miss_times = [times[0]] if times else []
        hit_times = times[1:] if len(times) > 1 else []
        
        return {
            'success': True,
            'times': times,
            'miss_times': miss_times,
            'hit_times': hit_times,
            'avg_time': statistics.mean(times),
            'avg_miss_time': statistics.mean(miss_times) if miss_times else 0,
            'avg_hit_time': statistics.mean(hit_times) if hit_times else 0,
            'min_time': min(times),
            'max_time': max(times),
            'std_dev': statistics.stdev(times) if len(times) > 1 else 0,
            'chunks': len(results[0].chunks) if results else 0,
            'pages': results[0].page_count if results else 0
        }
    
    async def run_comprehensive_test(self, max_files: int = 3, iterations: int = 3):
        """Run comprehensive cache performance test"""
        print("🚀 Starting Comprehensive Cache Performance Test")
        print("=" * 60)
        
        # Find test files
        test_files = self.find_test_files(max_files)
        print(f"📁 Selected {len(test_files)} test files:")
        for file_path in test_files:
            size_mb = file_path.stat().st_size / 1024 / 1024
            print(f"   • {file_path.name} ({size_mb:.1f}MB)")
        
        print()
        
        # Initialize cache service
        await start_cache_service()
        
        overall_results = {
            'files_tested': len(test_files),
            'iterations_per_test': iterations,
            'file_results': {},
            'summary': {}
        }
        
        total_no_cache_time = 0
        total_cache_miss_time = 0
        total_cache_hit_time = 0
        successful_tests = 0
        
        for file_path in test_files:
            print(f"\n📄 Testing file: {file_path.name}")
            print("-" * 40)
            
            # Test without cache
            no_cache_result = await self.test_without_cache(file_path, iterations)
            
            # Small delay between tests
            await asyncio.sleep(1)
            
            # Test with cache
            cache_result = await self.test_with_cache(file_path, iterations)
            
            if no_cache_result['success'] and cache_result['success']:
                successful_tests += 1
                
                # Calculate improvements
                cache_miss_overhead = ((cache_result['avg_miss_time'] - no_cache_result['avg_time']) / no_cache_result['avg_time']) * 100
                cache_hit_improvement = ((no_cache_result['avg_time'] - cache_result['avg_hit_time']) / no_cache_result['avg_time']) * 100 if cache_result['hit_times'] else 0
                speedup_factor = no_cache_result['avg_time'] / cache_result['avg_hit_time'] if cache_result['hit_times'] else 1
                
                file_summary = {
                    'no_cache': no_cache_result,
                    'cache': cache_result,
                    'cache_miss_overhead_pct': cache_miss_overhead,
                    'cache_hit_improvement_pct': cache_hit_improvement,
                    'speedup_factor': speedup_factor
                }
                
                overall_results['file_results'][file_path.name] = file_summary
                
                # Accumulate totals
                total_no_cache_time += no_cache_result['avg_time']
                total_cache_miss_time += cache_result['avg_miss_time']
                total_cache_hit_time += cache_result['avg_hit_time'] if cache_result['hit_times'] else 0
                
                print(f"   📊 Results for {file_path.name}:")
                print(f"      No cache: {no_cache_result['avg_time']:.2f}s avg")
                print(f"      Cache miss: {cache_result['avg_miss_time']:.2f}s")
                print(f"      Cache hit: {cache_result['avg_hit_time']:.2f}s avg" if cache_result['hit_times'] else "      Cache hit: N/A")
                print(f"      Miss overhead: {cache_miss_overhead:+.1f}%")
                print(f"      Hit improvement: {cache_hit_improvement:.1f}%")
                print(f"      Speedup factor: {speedup_factor:.1f}x")
            else:
                print(f"   ❌ Test failed for {file_path.name}")
        
        # Calculate overall summary
        if successful_tests > 0:
            avg_no_cache_time = total_no_cache_time / successful_tests
            avg_cache_miss_time = total_cache_miss_time / successful_tests
            avg_cache_hit_time = total_cache_hit_time / successful_tests if total_cache_hit_time > 0 else 0
            
            overall_miss_overhead = ((avg_cache_miss_time - avg_no_cache_time) / avg_no_cache_time) * 100
            overall_hit_improvement = ((avg_no_cache_time - avg_cache_hit_time) / avg_no_cache_time) * 100 if avg_cache_hit_time > 0 else 0
            overall_speedup = avg_no_cache_time / avg_cache_hit_time if avg_cache_hit_time > 0 else 1
            
            overall_results['summary'] = {
                'successful_tests': successful_tests,
                'avg_no_cache_time': avg_no_cache_time,
                'avg_cache_miss_time': avg_cache_miss_time,
                'avg_cache_hit_time': avg_cache_hit_time,
                'overall_miss_overhead_pct': overall_miss_overhead,
                'overall_hit_improvement_pct': overall_hit_improvement,
                'overall_speedup_factor': overall_speedup
            }
        
        # Display final summary
        print("\n" + "=" * 60)
        print("📊 OVERALL PERFORMANCE SUMMARY")
        print("=" * 60)
        
        if successful_tests > 0:
            summary = overall_results['summary']
            print(f"✅ Successfully tested {successful_tests}/{len(test_files)} files")
            print(f"📈 Performance Results:")
            print(f"   Average time without cache: {summary['avg_no_cache_time']:.2f}s")
            print(f"   Average time with cache miss: {summary['avg_cache_miss_time']:.2f}s")
            print(f"   Average time with cache hit: {summary['avg_cache_hit_time']:.2f}s")
            print(f"   Cache miss overhead: {summary['overall_miss_overhead_pct']:+.1f}%")
            print(f"   Cache hit improvement: {summary['overall_hit_improvement_pct']:.1f}%")
            print(f"   Overall speedup factor: {summary['overall_speedup_factor']:.1f}x")
            
            # Cache effectiveness assessment
            if summary['overall_hit_improvement_pct'] > 50:
                print(f"🎉 Cache is HIGHLY EFFECTIVE!")
            elif summary['overall_hit_improvement_pct'] > 20:
                print(f"✅ Cache is EFFECTIVE")
            elif summary['overall_hit_improvement_pct'] > 0:
                print(f"⚠️  Cache provides MODEST improvement")
            else:
                print(f"❌ Cache shows NO improvement")
        else:
            print("❌ No successful tests completed")
        
        # Get final cache stats
        cache_service = get_cache_service()
        cache_stats = await cache_service.get_stats()
        if cache_stats:
            print(f"\n📊 Final Cache Statistics:")
            print(f"   Entries: {cache_stats.total_entries}")
            print(f"   Size: {cache_stats.total_size_mb:.2f}MB")
            print(f"   Hit rate: {cache_stats.hit_rate:.2%}")
        
        await stop_cache_service()
        
        self.results = overall_results
        return overall_results

async def main():
    """Main test function"""
    tester = CachePerformanceTester()
    
    try:
        results = await tester.run_comprehensive_test(max_files=3, iterations=3)
        
        # Optionally save results to file
        import json
        results_file = Path("cache_performance_results.json")
        with open(results_file, 'w') as f:
            # Convert any non-serializable objects to strings
            serializable_results = json.loads(json.dumps(results, default=str))
            json.dump(serializable_results, f, indent=2)
        
        print(f"\n💾 Results saved to: {results_file}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Detailed error information:")

if __name__ == "__main__":
    asyncio.run(main())
