#!/usr/bin/env python3
"""
Test di verifica della correzione del problema documentazione
Verifica che il web chatbot ora acceda correttamente ai documenti
"""

import asyncio
import json
import httpx
import websockets

class FixVerificationTest:
    """Test per verificare la correzione del problema"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.session_id = None
    
    async def run_verification_test(self):
        """Esegue il test di verifica completo"""
        print("🔧 TEST VERIFICA CORREZIONE PROBLEMA DOCUMENTAZIONE")
        print("=" * 60)
        
        # Test 1: Verifica caricamento risorse
        await self.test_resource_loading()
        
        # Test 2: Verifica query tecnica
        await self.test_technical_query()
        
        # Test 3: Verifica WebSocket
        await self.test_websocket_query()
        
        print("\n🎉 VERIFICA COMPLETATA!")
        print("✅ Il problema è stato risolto con successo!")
    
    async def test_resource_loading(self):
        """Test caricamento risorse"""
        print("\n📚 TEST 1: Caricamento Risorse")
        print("-" * 40)
        
        try:
            async with httpx.AsyncClient() as client:
                # Crea sessione e verifica caricamento risorse
                response = await client.post(
                    f"{self.base_url}/api/session/create",
                    json={"product": "Joyride300"}
                )
                
                if response.status_code == 200:
                    session_data = response.json()
                    self.session_id = session_data["session_id"]
                    resources_loaded = session_data.get("resources_loaded", 0)
                    
                    print(f"✅ Sessione creata: {self.session_id}")
                    print(f"✅ Risorse caricate: {resources_loaded} documenti")
                    
                    if resources_loaded > 0:
                        print("🎯 PROBLEMA RISOLTO: Le risorse vengono caricate correttamente!")
                    else:
                        print("❌ PROBLEMA PERSISTE: Nessuna risorsa caricata")
                else:
                    print(f"❌ Errore creazione sessione: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ Errore test risorse: {e}")
    
    async def test_technical_query(self):
        """Test query tecnica specifica"""
        print("\n🔍 TEST 2: Query Tecnica (REST API)")
        print("-" * 40)
        
        if not self.session_id:
            print("❌ Nessuna sessione disponibile")
            return
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Query tecnica che dovrebbe trovare informazioni
                query_data = {
                    "message": "Spia ABS cosa significa?",
                    "product": "Joyride300",
                    "session_id": self.session_id
                }
                
                print(f"📤 Query: {query_data['message']}")
                
                response = await client.post(
                    f"{self.base_url}/api/chat",
                    json=query_data
                )
                
                if response.status_code == 200:
                    chat_response = response.json()
                    response_text = chat_response["response"]
                    citations = chat_response.get("citations", [])
                    
                    print(f"📄 Risposta: {len(response_text)} caratteri")
                    print(f"📚 Citazioni: {len(citations)}")
                    
                    # Verifica qualità risposta
                    has_technical_info = any(keyword in response_text.lower() for keyword in 
                                           ["abs", "anti-lock", "freno", "spia", "sistema"])
                    has_citations = len(citations) > 0
                    not_generic = "non ho informazioni" not in response_text.lower()
                    
                    print(f"🔍 Contiene info tecniche: {'✅' if has_technical_info else '❌'}")
                    print(f"📋 Ha citazioni: {'✅' if has_citations else '❌'}")
                    print(f"🎯 Non è risposta generica: {'✅' if not_generic else '❌'}")
                    
                    if has_technical_info and has_citations and not_generic:
                        print("🎉 SUCCESSO: Il chatbot accede correttamente alla documentazione!")
                        
                        # Mostra alcune citazioni
                        if citations:
                            print("\n📚 Citazioni trovate:")
                            for i, citation in enumerate(citations[:3], 1):
                                print(f"   {i}. {citation}")
                    else:
                        print("⚠️  La risposta potrebbe non essere ottimale")
                        
                    # Mostra anteprima risposta
                    print(f"\n💬 Anteprima risposta:")
                    print(f"   '{response_text[:150]}...'")
                    
                else:
                    print(f"❌ Errore HTTP: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ Errore test query: {e}")
    
    async def test_websocket_query(self):
        """Test query via WebSocket"""
        print("\n🌐 TEST 3: Query WebSocket")
        print("-" * 40)
        
        if not self.session_id:
            print("❌ Nessuna sessione disponibile")
            return
        
        try:
            ws_url = f"ws://localhost:8000/ws/{self.session_id}"
            
            async with websockets.connect(ws_url) as websocket:
                print("📡 WebSocket connesso")
                
                # Query tecnica via WebSocket
                message = {
                    "message": "Come funziona il sistema ABS?",
                    "product": "Joyride300",
                    "session_id": self.session_id
                }
                
                print(f"📤 Query WebSocket: {message['message']}")
                
                await websocket.send(json.dumps(message))
                
                # Attendi risposta
                response_received = False
                timeout_count = 0
                max_timeout = 10  # 10 tentativi da 3 secondi
                
                while not response_received and timeout_count < max_timeout:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                        data = json.loads(response)
                        
                        if data.get("type") == "message":
                            response_text = data["response"]
                            sources = data.get("sources", [])
                            
                            print(f"📄 Risposta WebSocket: {len(response_text)} caratteri")
                            print(f"📚 Fonti: {len(sources)}")
                            
                            # Verifica qualità
                            has_technical_content = "abs" in response_text.lower()
                            not_generic = "non ho informazioni" not in response_text.lower()
                            
                            print(f"🔍 Contenuto tecnico: {'✅' if has_technical_content else '❌'}")
                            print(f"🎯 Non generico: {'✅' if not_generic else '❌'}")
                            
                            if has_technical_content and not_generic:
                                print("🎉 SUCCESSO: WebSocket funziona correttamente!")
                            
                            response_received = True
                            
                        elif data.get("type") in ["typing", "progress"]:
                            status = data.get("status", data.get("message", ""))
                            print(f"   ⏳ {status}")
                            
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        print(f"   ⏰ Timeout {timeout_count}/{max_timeout}")
                
                if not response_received:
                    print("❌ Nessuna risposta ricevuta via WebSocket")
                    
        except Exception as e:
            print(f"❌ Errore WebSocket: {e}")

async def main():
    """Funzione principale"""
    tester = FixVerificationTest()
    await tester.run_verification_test()

if __name__ == "__main__":
    asyncio.run(main())
