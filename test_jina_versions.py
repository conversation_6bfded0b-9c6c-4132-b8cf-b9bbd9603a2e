#!/usr/bin/env python3
"""
Test script to check available Jina embedding models and their performance
"""

import asyncio
import httpx
import json
import os
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from config import config

async def test_jina_model(model_name: str, test_text: str = "Test embedding generation"):
    """Test a specific Jina model"""
    print(f"\n🧪 Testing model: {model_name}")
    
    try:
        client = httpx.AsyncClient(timeout=30.0)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {config.jina_api_key}"
        }
        
        payload = {
            "model": model_name,
            "task": "retrieval.passage",
            "dimensions": 1024,
            "input": [test_text]
        }
        
        response = await client.post(
            config.jina_api_url,
            headers=headers,
            json=payload
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if "data" in result and len(result["data"]) > 0:
                embedding = result["data"][0]["embedding"]
                print(f"   ✅ Success! Embedding dimensions: {len(embedding)}")
                print(f"   Model info: {result.get('model', 'N/A')}")
                return True, len(embedding)
            else:
                print(f"   ❌ No embedding data returned")
                return False, 0
        else:
            print(f"   ❌ Error: {response.text}")
            return False, 0
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False, 0
    finally:
        await client.aclose()

async def test_jina_models():
    """Test different Jina embedding models"""
    print("🔍 Testing Jina Embedding Models")
    print("=" * 50)
    
    # Models to test (in order of preference)
    models_to_test = [
        "jina-embeddings-v4",  # Latest v4 model
        "jina-embeddings-v3",
        "jina-embeddings-v2-base-en",
        "jina-embeddings-v2-small-en",
        "jina-clip-v1"  # Multimodal model
    ]
    
    # Test text (technical automotive content)
    test_text = "Misurare il diametro interno del bilanciere e sostituire se eccede dai parametri. Limite: 10.100mm"
    
    results = {}
    
    for model in models_to_test:
        success, dimensions = await test_jina_model(model, test_text)
        results[model] = {
            "success": success,
            "dimensions": dimensions
        }
        
        # Small delay between tests
        await asyncio.sleep(1)
    
    print("\n" + "=" * 50)
    print("📊 RESULTS SUMMARY")
    print("=" * 50)
    
    working_models = []
    for model, result in results.items():
        status = "✅ Working" if result["success"] else "❌ Failed"
        dims = f"({result['dimensions']}D)" if result["success"] else ""
        print(f"{model:<30} {status} {dims}")
        
        if result["success"]:
            working_models.append((model, result["dimensions"]))
    
    if working_models:
        print(f"\n🎯 RECOMMENDATION:")
        # Sort by dimensions (higher is generally better for accuracy)
        working_models.sort(key=lambda x: x[1], reverse=True)
        best_model = working_models[0]
        print(f"   Use: {best_model[0]} ({best_model[1]} dimensions)")
        
        # Check if we should update the code
        current_model = "jina-embeddings-v3"
        if best_model[0] != current_model:
            print(f"\n⚠️  Current code uses: {current_model}")
            print(f"   Consider updating to: {best_model[0]}")
    else:
        print("\n❌ No working models found!")
    
    return working_models

async def test_specific_automotive_content():
    """Test with specific automotive technical content"""
    print("\n" + "=" * 50)
    print("🔧 Testing with Automotive Technical Content")
    print("=" * 50)
    
    # Technical automotive texts in Italian
    test_texts = [
        "Misurare il diametro interno del bilanciere e sostituire se eccede dai parametri. Limite: 10.100mm",
        "Controllare la pressione dell'olio motore e verificare il funzionamento della pompa",
        "Regolare il gioco valvole secondo le specifiche tecniche del costruttore",
        "Verificare l'usura dei cuscinetti dell'albero motore e sostituire se necessario"
    ]
    
    # Test with the best available model
    model = "jina-embeddings-v4"  # We'll use v4 as default
    
    try:
        client = httpx.AsyncClient(timeout=30.0)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {config.jina_api_key}"
        }
        
        payload = {
            "model": model,
            "task": "retrieval.passage",
            "dimensions": 1024,
            "input": test_texts
        }
        
        response = await client.post(
            config.jina_api_url,
            headers=headers,
            json=payload
        )
        
        if response.status_code == 200:
            result = response.json()
            embeddings = [item["embedding"] for item in result["data"]]
            
            print(f"✅ Successfully generated embeddings for {len(embeddings)} automotive texts")
            print(f"   Embedding dimensions: {len(embeddings[0])}")
            
            # Calculate similarity between first two texts
            import numpy as np
            
            emb1 = np.array(embeddings[0])
            emb2 = np.array(embeddings[1])
            
            # Cosine similarity
            similarity = np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))
            print(f"   Similarity between text 1 and 2: {similarity:.3f}")
            
            return True
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False
    finally:
        await client.aclose()

if __name__ == "__main__":
    async def main():
        print("🚀 Jina Embeddings API Test Suite")
        print("=" * 50)
        
        # Test different models
        working_models = await test_jina_models()
        
        # Test with automotive content
        if working_models:
            await test_specific_automotive_content()
        
        print("\n✅ Test completed!")
    
    asyncio.run(main())
