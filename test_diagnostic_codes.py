#!/usr/bin/env python3
"""
Test script for diagnostic codes functionality
Tests the fix for diagnostic codes being incorrectly blocked
"""

import asyncio
import logging
from security_utils import domain_validator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_diagnostic_codes():
    """Test diagnostic codes functionality"""
    print("=== Diagnostic Codes Test ===")
    print("Testing the fix for diagnostic codes being incorrectly blocked")
    
    # Test various diagnostic code formats
    test_queries = [
        # Basic diagnostic codes
        ("codice guasto P0116", True, "Temperature sensor diagnostic code"),
        ("P0420", True, "Catalyst efficiency diagnostic code"),
        ("codice errore P0108", True, "MAP sensor diagnostic code"),
        ("P0261", True, "Injector diagnostic code"),
        ("codici guasto", True, "General diagnostic codes term"),
        ("errore P0136", True, "Oxygen sensor diagnostic code"),
        ("diagnostico P0230", True, "Fuel pump diagnostic code"),
        
        # OBD-II codes from documents
        ("P0106", True, "MAP sensor code from documents"),
        ("P0114", True, "Temperature sensor code from documents"),
        ("P0485", True, "Fan control code from documents"),
        ("P0314", True, "Misfire code from documents"),
        
        # Generic diagnostic patterns
        ("C1032", True, "ABS diagnostic code"),
        ("B0001", True, "Body control diagnostic code"),
        ("U0100", True, "Network diagnostic code"),
        
        # Compound diagnostic terms
        ("codice guasto centralina", True, "ECU diagnostic code"),
        ("errore sistema ABS", True, "ABS system error"),
        ("diagnosi motore", True, "Engine diagnostics"),
        
        # Non-automotive codes (should still be blocked)
        ("codice HTML", False, "Programming code - should be blocked"),
        ("codice Python", False, "Programming code - should be blocked"),
        ("codice sorgente", False, "Source code - should be blocked"),
        
        # Edge cases
        ("codice", True, "Just 'codice' should be allowed (automotive context)"),
        ("guasto", True, "Just 'guasto' should be allowed"),
        ("errore", True, "Just 'errore' should be allowed"),
    ]
    
    print(f"\nTesting {len(test_queries)} diagnostic code queries...")
    
    passed = 0
    total = len(test_queries)
    
    for query, expected_allowed, description in test_queries:
        is_relevant, error = domain_validator.validate_domain_relevance(query)
        
        if is_relevant == expected_allowed:
            status = "✓ PASS"
            passed += 1
        else:
            status = "✗ FAIL"
        
        allowed_str = "ALLOWED" if is_relevant else "BLOCKED"
        expected_str = "should be ALLOWED" if expected_allowed else "should be BLOCKED"
        
        print(f"{status}: '{query}' -> {allowed_str} ({expected_str}) - {description}")
        if error and not is_relevant:
            print(f"      Reason: {error}")
    
    print(f"\n=== Results ===")
    print(f"Passed: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All diagnostic code tests passed!")
        print("✅ The fix for diagnostic codes is working correctly.")
    else:
        print("⚠️  Some diagnostic code tests failed.")
        print("❌ The fix may need further adjustments.")
    
    # Show specific diagnostic codes found in whitelist
    print(f"\n=== Diagnostic codes in whitelist ===")
    diagnostic_codes = [term for term in domain_validator.document_whitelist 
                       if term.startswith('p0') or term.startswith('c1') or 'codice' in term][:15]
    
    if diagnostic_codes:
        print("Sample diagnostic codes extracted from documents:")
        for code in diagnostic_codes:
            print(f"  - {code}")
    else:
        print("No diagnostic codes found in whitelist")

if __name__ == "__main__":
    asyncio.run(test_diagnostic_codes())
