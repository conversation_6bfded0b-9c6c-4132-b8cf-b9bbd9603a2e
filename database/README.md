# Schema Database per Log Chatbot

Questo repository contiene lo schema del database MySQL per il logging delle conversazioni del chatbot, progettato per essere efficiente, scalabile e ottimizzato per l'analisi delle performance.

## 📋 Struttura Database

### Informazioni di Connessione
Le credenziali di connessione sono configurate nel file `.env`:
- **Host**: localhost
- **Porta**: 3306
- **Database**: softway_chat
- **Utente**: prova
- **Password**: prova
- **Charset**: utf8mb4
- **Collation**: utf8mb4_unicode_ci

### Tabella: `chatbot_logs`

| Campo | Tipo | Constraint | Descrizione |
|-------|------|------------|-------------|
| `id` | INT | AUTO_INCREMENT PRIMARY KEY | Chiave primaria |
| `created_at` | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Timestamp creazione |
| `ip_address` | VARCHAR(45) | NULL | IP utente (IPv4/IPv6) |
| `session_id` | VARCHAR(255) | NOT NULL | ID sessione conversazione |
| `product` | VARCHAR(100) | NULL | Linea di prodotto |
| `interface_type` | ENUM('CLI','WEB') | NOT NULL, DEFAULT 'CLI' | Tipo interfaccia |
| `question` | TEXT | NOT NULL | Domanda utente |
| `answer` | MEDIUMTEXT | NULL | Risposta chatbot |
| `search_result_count` | INT | DEFAULT 0 | Numero documenti utilizzati |
| `avg_relevance_score` | DECIMAL(5,4) | NULL | Punteggio pertinenza medio |
| `used_general_knowledge` | BOOLEAN | NOT NULL, DEFAULT FALSE | Uso conoscenza generale |
| `response_time_ms` | INT | NULL | Tempo risposta (ms) |
| `confidence` | DECIMAL(5,4) | NULL | Confidenza risposta |
| `tool_executions` | JSON | NULL | Log strumenti eseguiti |

## 🚀 Installazione

### 1. Creazione Schema
```bash
# Connessione a MySQL
mysql -h localhost -u prova -p

# Esecuzione script di creazione
source database/create_chatbot_logs_table.sql
```

### 2. Test e Verifica
```bash
# Esecuzione script di test
source database/test_chatbot_logs.sql
```

## 📊 Indici Ottimizzati

Gli indici sono stati creati per ottimizzare le query più comuni:

- `idx_session_id`: Query per sessione specifica
- `idx_created_at`: Query temporali e analisi cronologiche
- `idx_product`: Filtraggio per linea di prodotto
- `idx_session_time`: Query combinate sessione + tempo
- `idx_response_time`: Analisi performance
- `idx_interface_type`: Filtraggio per tipo interfaccia (CLI/WEB)

## 🔍 Query di Esempio

### Analisi Performance
```sql
-- Tempo medio di risposta per prodotto
SELECT 
    product,
    AVG(response_time_ms) as avg_response_time,
    COUNT(*) as total_conversations
FROM chatbot_logs 
WHERE product IS NOT NULL
GROUP BY product;
```

### Analisi Qualità Risposte
```sql
-- Distribuzione confidenza per tipo di risposta
SELECT 
    CASE 
        WHEN used_general_knowledge = TRUE THEN 'Conoscenza Generale'
        ELSE 'Documenti Specifici'
    END as response_type,
    AVG(confidence) as avg_confidence,
    COUNT(*) as count
FROM chatbot_logs 
GROUP BY used_general_knowledge;
```

### Analisi Temporale
```sql
-- Conversazioni per ora nelle ultime 24h
SELECT 
    DATE_FORMAT(created_at, '%H:00') as hour,
    COUNT(*) as conversations
FROM chatbot_logs 
WHERE created_at >= NOW() - INTERVAL 24 HOUR
GROUP BY hour
ORDER BY hour;
```

### Analisi per Tipo di Interfaccia
```sql
-- Distribuzione conversazioni per interfaccia
SELECT
    interface_type,
    COUNT(*) as conversations,
    AVG(response_time_ms) as avg_response_time,
    AVG(confidence) as avg_confidence
FROM chatbot_logs
GROUP BY interface_type;

-- Confronto performance CLI vs WEB
SELECT
    interface_type,
    AVG(response_time_ms) as avg_response_time,
    AVG(search_result_count) as avg_search_results,
    SUM(CASE WHEN used_general_knowledge THEN 1 ELSE 0 END) / COUNT(*) * 100 as general_knowledge_percentage
FROM chatbot_logs
WHERE created_at >= NOW() - INTERVAL 7 DAY
GROUP BY interface_type;
```

### Analisi Strumenti
```sql
-- Strumenti più utilizzati
SELECT
    JSON_UNQUOTE(JSON_EXTRACT(tool_executions, '$.tools_used[0]')) as primary_tool,
    COUNT(*) as usage_count
FROM chatbot_logs
WHERE tool_executions IS NOT NULL
GROUP BY primary_tool
ORDER BY usage_count DESC;
```

## 📈 Metriche di Monitoraggio

### KPI Principali
- **Tempo di Risposta Medio**: `AVG(response_time_ms)`
- **Confidenza Media**: `AVG(confidence)`
- **Tasso Uso Conoscenza Generale**: `SUM(used_general_knowledge)/COUNT(*)`
- **Documenti Medi per Risposta**: `AVG(search_result_count)`

### Query di Monitoraggio
```sql
-- Dashboard metriche giornaliere
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_conversations,
    AVG(response_time_ms) as avg_response_time,
    AVG(confidence) as avg_confidence,
    AVG(search_result_count) as avg_documents_used,
    SUM(CASE WHEN used_general_knowledge THEN 1 ELSE 0 END) / COUNT(*) * 100 as general_knowledge_percentage
FROM chatbot_logs 
WHERE created_at >= CURDATE() - INTERVAL 7 DAY
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

## 🛠️ Manutenzione

### Pulizia Dati Vecchi
```sql
-- Rimozione log più vecchi di 90 giorni
DELETE FROM chatbot_logs 
WHERE created_at < NOW() - INTERVAL 90 DAY;
```

### Ottimizzazione Tabella
```sql
-- Ottimizzazione periodica
OPTIMIZE TABLE chatbot_logs;
```

## 🔒 Considerazioni di Sicurezza

- I dati sensibili negli IP sono mascherati se necessario
- Le conversazioni possono contenere informazioni sensibili
- Implementare rotazione dei log per conformità GDPR
- Considerare crittografia per campi sensibili in produzione

## 📝 Note Tecniche

- **Motore**: InnoDB per transazioni ACID
- **Charset**: utf8mb4 per supporto Unicode completo
- **JSON**: Campo `tool_executions` per flessibilità nel logging
- **Scalabilità**: Indici ottimizzati per query frequenti
- **Performance**: DECIMAL per precisione numerica nelle metriche
