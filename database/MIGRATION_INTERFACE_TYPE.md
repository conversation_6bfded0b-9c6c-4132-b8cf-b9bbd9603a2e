# Migrazione: Aggiunta Colonna interface_type

Questa migrazione aggiunge la colonna `interface_type` alla tabella `chatbot_logs` per distinguere tra conversazioni CLI e WEB.

## 🎯 Obiettivo

Aggiungere la capacità di distinguere e analizzare separatamente le conversazioni provenienti da:
- **CLI**: Interfaccia a riga di comando
- **WEB**: Interfaccia web (WebSocket e REST API)

## 📋 Modifiche Apportate

### Schema Database
```sql
-- Nuova colonna
interface_type ENUM('CLI', 'WEB') NOT NULL DEFAULT 'CLI'

-- Nuovo indice
CREATE INDEX idx_interface_type ON chatbot_logs(interface_type);
```

### Logica Applicazione
- **CLI**: Sempre `interface_type = 'CLI'`
- **WEB**: Sempre `interface_type = 'WEB'`
- **Default**: 'CLI' per compatibilità con dati esistenti

## 🚀 Come Applicare la Migrazione

### Per Database Nuovi
I nuovi database creati con `create_chatbot_logs_table.sql` includeranno automaticamente la colonna.

### Per Database Esistenti
```bash
# Applica la migrazione
mysql -u prova -p < database/migrate_add_interface_type.sql
```

### Verifica Migrazione
```bash
# Testa che tutto funzioni
python test_interface_type.py
```

## 📊 Analisi Disponibili

### Query di Base
```sql
-- Distribuzione per interfaccia
SELECT 
    interface_type,
    COUNT(*) as conversations,
    AVG(response_time_ms) as avg_response_time
FROM chatbot_logs 
GROUP BY interface_type;
```

### Confronto Performance
```sql
-- CLI vs WEB performance
SELECT 
    interface_type,
    AVG(response_time_ms) as avg_response_time,
    AVG(confidence) as avg_confidence,
    AVG(search_result_count) as avg_search_results
FROM chatbot_logs 
WHERE created_at >= NOW() - INTERVAL 7 DAY
GROUP BY interface_type;
```

### Trend Temporali
```sql
-- Utilizzo per ora e interfaccia
SELECT 
    DATE_FORMAT(created_at, '%H:00') as hour,
    interface_type,
    COUNT(*) as conversations
FROM chatbot_logs 
WHERE created_at >= NOW() - INTERVAL 24 HOUR
GROUP BY hour, interface_type
ORDER BY hour, interface_type;
```

## 🔧 Dettagli Tecnici

### Logica di Assegnazione
La migrazione assegna automaticamente i valori basandosi su euristiche:
- `ip_address IN ('127.0.0.1', '::1', 'localhost')` o `NULL` → `'CLI'`
- Altri IP → `'WEB'`

### Compatibilità
- **Backward Compatible**: I dati esistenti vengono preservati
- **Default Value**: 'CLI' per nuovi record senza specifica
- **Non-Breaking**: L'applicazione continua a funzionare durante la migrazione

### Performance
- **Indice Ottimizzato**: `idx_interface_type` per query veloci
- **ENUM Type**: Efficiente in termini di storage
- **Overhead Minimo**: < 1 byte per record

## 🧪 Testing

### Test Automatici
```bash
# Test completo della funzionalità
python test_interface_type.py

# Output atteso:
# ✅ PASS Migration Compatibility
# ✅ PASS CLI Interface Logging  
# ✅ PASS WEB Interface Logging
# ✅ PASS Database Verification
# ✅ PASS Interface Type Statistics
```

### Test Manuali

1. **CLI Test**:
   ```bash
   python main.py
   # Fai una domanda, poi verifica:
   SELECT interface_type FROM chatbot_logs ORDER BY created_at DESC LIMIT 1;
   # Dovrebbe essere 'CLI'
   ```

2. **WEB Test**:
   ```bash
   python start_web.py
   # Apri http://localhost:8000, fai una domanda, poi verifica:
   SELECT interface_type FROM chatbot_logs ORDER BY created_at DESC LIMIT 1;
   # Dovrebbe essere 'WEB'
   ```

## 📈 Benefici

### Analytics Migliorati
- **Segmentazione Utenti**: Comportamenti diversi CLI vs WEB
- **Performance Comparison**: Tempi di risposta per interfaccia
- **Usage Patterns**: Preferenze degli utenti

### Monitoring
- **Separate Dashboards**: Metriche specifiche per interfaccia
- **Alerting**: Problemi specifici CLI o WEB
- **Capacity Planning**: Carico per tipo di interfaccia

### Business Intelligence
- **User Experience**: Quale interfaccia è più efficace
- **Feature Usage**: Funzionalità più usate per interfaccia
- **Support**: Problemi specifici per tipo di accesso

## 🔄 Rollback (se necessario)

Se dovessi rimuovere la colonna:
```sql
-- ATTENZIONE: Questo elimina i dati della colonna
ALTER TABLE chatbot_logs DROP COLUMN interface_type;
DROP INDEX idx_interface_type ON chatbot_logs;
```

## 📝 Note di Implementazione

### Codice Modificato
- `database_logger.py`: Aggiunto campo `interface_type`
- `session_manager.py`: Logica per assegnazione tipo
- `web_server.py`: Sempre `interface_type='WEB'`
- `main.py`: Sempre `interface_type='CLI'`

### Configurazione
Nessuna configurazione aggiuntiva richiesta. Il sistema rileva automaticamente il tipo di interfaccia.

### Monitoraggio
Usa `monitor_database.py` per vedere le statistiche in tempo reale con la nuova colonna.

## ✅ Checklist Post-Migrazione

- [ ] Migrazione applicata senza errori
- [ ] Test automatici passano tutti
- [ ] CLI logga come 'CLI'
- [ ] WEB logga come 'WEB'  
- [ ] Query di analytics funzionano
- [ ] Monitor mostra entrambi i tipi
- [ ] Performance non degradate

## 🎉 Conclusione

La colonna `interface_type` fornisce una dimensione di analisi fondamentale per comprendere l'utilizzo del chatbot attraverso diverse interfacce, permettendo ottimizzazioni mirate e una migliore user experience.
