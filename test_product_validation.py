#!/usr/bin/env python3
"""
Test script for product name validation with spaces
Tests that product names with spaces are correctly validated
"""

import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from security_utils import security_validator
from config import config

def test_product_validation():
    """Test product name validation with various cases"""
    print("🔍 Testing Product Name Validation")
    print("=" * 50)
    
    test_cases = [
        # (product_name, should_be_valid, description)
        ("Symphony ST 200", True, "Product with spaces"),
        ("Joyride300", True, "Product without spaces"),
        ("Symphony-ST-200", True, "Product with hyphens"),
        ("Symphony_ST_200", True, "Product with underscores"),
        ("../etc/passwd", False, "Path traversal attempt"),
        ("Symphony/ST/200", False, "Product with slashes"),
        ("Symphony\\ST\\200", False, "Product with backslashes"),
        ("Symphony<script>", False, "Product with dangerous characters"),
        ("Symphony|ST|200", False, "Product with pipe characters"),
        ("Symphony:ST:200", False, "Product with colons"),
        ("Symphony*ST*200", False, "Product with asterisks"),
        ("Symphony?ST?200", False, "Product with question marks"),
        ('Symphony"ST"200', False, "Product with quotes"),
        ("", False, "Empty product name"),
        ("   ", False, "Whitespace only product name"),
        ("A" * 101, False, "Product name too long"),
        ("NonExistentProduct", False, "Product directory doesn't exist"),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for product_name, expected_valid, description in test_cases:
        print(f"\n📋 Testing: {description}")
        print(f"   Product: '{product_name}'")
        
        try:
            is_valid, error_msg = security_validator.validate_product_name(product_name)
            
            if is_valid == expected_valid:
                status = "✅ PASS"
                passed += 1
            else:
                status = "❌ FAIL"
                print(f"   Expected: {expected_valid}, Got: {is_valid}")
                if error_msg:
                    print(f"   Error: {error_msg}")
            
            print(f"   Result: {status}")
            
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"📊 VALIDATION TEST RESULTS")
    print(f"=" * 50)
    print(f"Passed: {passed}/{total} tests")
    
    if passed == total:
        print("🎉 All product validation tests passed!")
    else:
        print("⚠️ Some validation tests failed.")
    
    return passed == total

def test_existing_products():
    """Test validation of existing products in sorgenti directory"""
    print(f"\n🔍 Testing Existing Products in {config.sorgenti_path}")
    print("=" * 50)
    
    try:
        sorgenti_path = config.sorgenti_path
        if not sorgenti_path.exists():
            print(f"❌ Sorgenti directory not found: {sorgenti_path}")
            return False
        
        products = [d.name for d in sorgenti_path.iterdir() if d.is_dir()]
        
        if not products:
            print("⚠️ No products found in sorgenti directory")
            return True
        
        print(f"Found {len(products)} products:")
        
        all_valid = True
        for product in products:
            print(f"\n📋 Testing existing product: '{product}'")
            
            is_valid, error_msg = security_validator.validate_product_name(product)
            
            if is_valid:
                print(f"   ✅ VALID")
            else:
                print(f"   ❌ INVALID: {error_msg}")
                all_valid = False
        
        if all_valid:
            print(f"\n🎉 All {len(products)} existing products are valid!")
        else:
            print(f"\n⚠️ Some existing products failed validation.")
        
        return all_valid
        
    except Exception as e:
        print(f"❌ Error testing existing products: {e}")
        return False

def test_url_encoding():
    """Test URL encoding scenarios"""
    print(f"\n🔍 Testing URL Encoding Scenarios")
    print("=" * 50)
    
    # Test URL encoded product names
    import urllib.parse
    
    test_cases = [
        ("Symphony ST 200", "Symphony%20ST%20200", "Space encoded as %20"),
        ("Symphony-ST-200", "Symphony-ST-200", "Hyphens don't need encoding"),
        ("Symphony_ST_200", "Symphony_ST_200", "Underscores don't need encoding"),
    ]
    
    for original, encoded, description in test_cases:
        print(f"\n📋 {description}")
        print(f"   Original: '{original}'")
        print(f"   URL Encoded: '{encoded}'")
        print(f"   URL Decoded: '{urllib.parse.unquote(encoded)}'")
        
        # Test that decoding gives us back the original
        decoded = urllib.parse.unquote(encoded)
        if decoded == original:
            print(f"   ✅ Encoding/Decoding works correctly")
        else:
            print(f"   ❌ Encoding/Decoding mismatch")
    
    return True

def main():
    """Main test function"""
    print("🚀 Product Name Validation Tests")
    print("=" * 60)
    
    try:
        # Run all tests
        validation_ok = test_product_validation()
        existing_ok = test_existing_products()
        encoding_ok = test_url_encoding()
        
        print(f"\n" + "=" * 60)
        print("📊 OVERALL TEST RESULTS")
        print("=" * 60)
        
        results = [
            ("Product Validation", validation_ok),
            ("Existing Products", existing_ok),
            ("URL Encoding", encoding_ok),
        ]
        
        all_passed = True
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
            if not result:
                all_passed = False
        
        if all_passed:
            print(f"\n🎉 All tests passed! Product names with spaces should now work.")
            print(f"\nYou can now access:")
            print(f"http://127.0.0.1:8000/api/pdf/Symphony%20ST%20200/Manuale%20officina%20Symphony%20ST%20200%20E5.pdf#page=4")
        else:
            print(f"\n⚠️ Some tests failed. Please check the issues above.")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
