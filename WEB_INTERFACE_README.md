# 🌐 Web Interface per Chatbot Tecnico

Interfaccia web moderna e responsive per il chatbot di assistenza tecnica basato su MCP (Model Context Protocol).

## 🚀 Avvio Rapido

### 1. Installazione Dipendenze
```bash
pip install -r requirements.txt
```

### 2. Avvio del Server Web
```bash
python start_web.py
```

### 3. Accesso all'Interfaccia
Apri il browser e vai su: **http://localhost:8000**

## 📋 Funzionalità

### ✨ Interfaccia Utente
- **Design iOS-inspired**: Interfaccia pulita e minimalista
- **Chat in tempo reale**: Comunicazione WebSocket per risposte immediate
- **Selezione prodotto**: Interfaccia intuitiva per scegliere il prodotto
- **Indicatori di stato**: Loading, typing indicator, stati di connessione
- **Gestione errori**: Messaggi di errore chiari con opzioni di retry
- **Responsive**: Ottimizzata per desktop e mobile

### 🔧 Funzionalità Chat
- **Messaggi in tempo reale**: WebSocket per comunicazione istantanea
- **Citazioni**: Visualizzazione delle fonti con modal dedicata
- **Cronologia**: Mantenimento della conversazione
- **Esportazione**: Salvataggio della chat in formato testo
- **Menu contestuale**: Opzioni per riavvio, pulizia, esportazione

### 🛠 Menu Opzioni
- **Riavvia Chat**: Ricomincia la conversazione
- **Svuota Chat**: Cancella tutti i messaggi
- **Esporta Chat**: Salva la conversazione
- **Info**: Informazioni sulla sessione corrente

## 🏗 Architettura

### Backend (FastAPI)
- **Web Server**: `web_server.py` - Server FastAPI con WebSocket
- **API REST**: Endpoints per gestione sessioni e prodotti
- **WebSocket**: Comunicazione in tempo reale
- **Integrazione MCP**: Connessione con il backend esistente

### Frontend
- **HTML**: `frontend/templates/index.html` - Struttura della pagina
- **CSS**: `frontend/templates/style.css` - Stili iOS-inspired
- **JavaScript**: `frontend/templates/script.js` - Logica dell'interfaccia

### API Endpoints

#### REST API
- `GET /` - Serve l'interfaccia web
- `GET /api/health` - Health check del server
- `GET /api/products` - Lista prodotti disponibili
- `POST /api/session/create` - Crea nuova sessione
- `POST /api/chat` - Invia messaggio (alternativa REST)
- `DELETE /api/session/{id}` - Elimina sessione

#### WebSocket
- `WS /ws/{session_id}` - Connessione chat in tempo reale

## 🧪 Testing

### Test di Integrazione
```bash
python test_web_integration.py
```

Il test verifica:
- ✅ Health check del server
- ✅ API dei prodotti
- ✅ Creazione sessioni
- ✅ Connessione WebSocket
- ✅ Invio messaggi chat
- ✅ Servizio file statici

### Test Manuale
1. Apri http://localhost:8000
2. Seleziona un prodotto
3. Invia un messaggio tecnico
4. Verifica la risposta con citazioni
5. Testa le funzionalità del menu

## 📁 Struttura File

```
├── web_server.py              # Server FastAPI principale
├── start_web.py               # Script di avvio semplificato
├── test_web_integration.py    # Test di integrazione completi
├── frontend/templates/
│   ├── index.html            # Interfaccia principale
│   ├── style.css             # Stili CSS
│   └── script.js             # Logica JavaScript
└── WEB_INTERFACE_README.md   # Questa documentazione
```

## 🎨 Personalizzazione

### Colori e Stili
Modifica `frontend/templates/style.css` per personalizzare:
- Colori delle bolle chat
- Font e dimensioni
- Layout responsive
- Animazioni

### Funzionalità JavaScript
Modifica `frontend/templates/script.js` per:
- Aggiungere nuove funzionalità
- Modificare comportamenti
- Integrare servizi esterni

## 🔧 Configurazione

### Variabili Ambiente
Le stesse del backend principale (file `.env`):
- `GOOGLE_API_KEY` - Chiave API Gemini
- `JINA-API-KEY` - Chiave API Jina
- Altri parametri di configurazione

### Porta del Server
Modifica in `start_web.py` o `web_server.py`:
```python
uvicorn.run("web_server:app", host="0.0.0.0", port=8000)
```

## 🚨 Risoluzione Problemi

### Server non si avvia
- Verifica che le dipendenze siano installate
- Controlla che il file `.env` esista
- Verifica che la porta 8000 sia libera

### Frontend non carica
- Controlla i percorsi dei file statici
- Verifica la console del browser per errori
- Riavvia il server con `python start_web.py`

### WebSocket non funziona
- Verifica la connessione di rete
- Controlla i log del server
- Prova l'API REST come alternativa

### Errori di sessione
- Verifica che il prodotto selezionato sia valido
- Controlla i log del backend MCP
- Riavvia la chat dal menu opzioni

## 📊 Monitoraggio

### Health Check
```bash
curl http://localhost:8000/api/health
```

### Log del Server
I log sono visibili nella console dove hai avviato `start_web.py`

### Metriche
- Connessioni WebSocket attive
- Sessioni create
- Cache hit rate
- Tempo di risposta

## 🔒 Sicurezza

- **Rate limiting**: Implementato nel backend
- **Validazione input**: Sanitizzazione messaggi
- **CORS**: Configurato per sviluppo
- **WebSocket sicuro**: Gestione errori e disconnessioni

## 🚀 Deployment

Per produzione, considera:
- Reverse proxy (nginx)
- HTTPS/WSS
- Configurazione CORS specifica
- Monitoring e logging avanzati
- Load balancing per scalabilità

---

**Sviluppato con ❤️ utilizzando FastAPI, WebSocket e design iOS-inspired**
