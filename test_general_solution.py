#!/usr/bin/env python3
"""
Test script for the general solution to handle any automotive term from documents
"""

import asyncio
import logging
from security_utils import domain_validator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_general_solution():
    """Test the general solution for automotive terms"""
    print("=== General Solution Test ===")
    print("Testing that ANY automotive term from documents works, not just specific cases")
    
    # Get current stats
    stats = domain_validator.get_whitelist_stats()
    print(f"Current whitelist size: {stats['whitelist_size']}")
    
    # Test cases that demonstrate the general solution
    test_queries = [
        # === TERMS FROM FILENAMES ===
        ("antenna", True, "From filename: 'Posizionamento antenna Keyless XS.pdf'"),
        ("keyless", True, "From filename: 'Posizionamento antenna Keyless XS.pdf'"),
        ("posizionamento", True, "From filename: 'Posizionamento antenna Keyless XS.pdf'"),
        ("joyride", True, "From filename: 'Joyride 300 E5+ Manuale uso e manutenzione.pdf'"),
        ("montaggio", True, "From filename: 'Montaggio Shad39 su Joyride 300.pdf'"),
        ("accoppiamento", True, "From filename: 'Procedure accoppiamento Veicoli...'"),
        ("wiring", True, "From filename: '17 Wiring diagram.pdf'"),
        ("diagram", True, "From filename: '17 Wiring diagram.pdf'"),
        
        # === SINGLE TECHNICAL TERMS (new logic) ===
        ("centralina", True, "Single technical term - should be allowed"),
        ("sensore", True, "Single technical term - should be allowed"),
        ("iniettore", True, "Single technical term - should be allowed"),
        ("alternatore", True, "Single technical term - should be allowed"),
        ("carburatore", True, "Single technical term - should be allowed"),
        ("radiatore", True, "Single technical term - should be allowed"),
        
        # === TERMS FROM DOCUMENT CONTENT ===
        ("silkolene", True, "Brand from document content"),
        ("synerject", True, "System from document content"),
        ("symphony", True, "Model from document content"),
        
        # === DIAGNOSTIC CODES ===
        ("P0116", True, "Diagnostic code from documents"),
        ("P0420", True, "Diagnostic code from documents"),
        
        # === NON-AUTOMOTIVE (should still be blocked) ===
        ("pizza", False, "Food - should be blocked"),
        ("ciao", False, "Greeting - should be blocked"),
        ("hello", False, "Greeting - should be blocked"),
        ("programmazione", False, "Programming - should be blocked"),
        ("cucina", False, "Cooking - should be blocked"),
        
        # === PROGRAMMING CONTEXTS (should be blocked) ===
        ("codice sorgente", False, "Programming context - should be blocked"),
        ("codice HTML", False, "Programming context - should be blocked"),
        
        # === AUTOMOTIVE CONTEXTS (should work) ===
        ("codice guasto", True, "Automotive context - should work"),
        ("sorgente del rumore", True, "Automotive context - should work"),
    ]
    
    print(f"\nTesting {len(test_queries)} queries...")
    
    passed = 0
    total = len(test_queries)
    
    # Track categories
    categories = {
        "Filename Terms": (0, 8),      # First 8 tests
        "Single Technical": (8, 6),    # Next 6 tests  
        "Document Content": (14, 3),   # Next 3 tests
        "Diagnostic Codes": (17, 2),   # Next 2 tests
        "Non-Automotive": (19, 5),     # Next 5 tests
        "Context-Sensitive": (24, 4)   # Last 4 tests
    }
    
    category_results = {}
    
    for i, (query, expected_allowed, description) in enumerate(test_queries):
        is_relevant, error = domain_validator.validate_domain_relevance(query)
        
        # Determine category
        current_category = None
        for cat_name, (start_idx, count) in categories.items():
            if start_idx <= i < start_idx + count:
                current_category = cat_name
                break
        
        if current_category not in category_results:
            category_results[current_category] = {"passed": 0, "total": 0}
        
        category_results[current_category]["total"] += 1
        
        if is_relevant == expected_allowed:
            status = "✓ PASS"
            passed += 1
            category_results[current_category]["passed"] += 1
        else:
            status = "✗ FAIL"
        
        allowed_str = "ALLOWED" if is_relevant else "BLOCKED"
        expected_str = "should be ALLOWED" if expected_allowed else "should be BLOCKED"
        
        print(f"{status}: '{query}' -> {allowed_str} ({expected_str})")
        print(f"      {description}")
        if error and not is_relevant:
            print(f"      Reason: {error}")
        print()
    
    print(f"=== Overall Results ===")
    print(f"Total: {passed}/{total} ({passed/total*100:.1f}%)")
    
    print(f"\n=== Results by Category ===")
    all_passed = True
    for category, results in category_results.items():
        p = results["passed"]
        t = results["total"]
        percentage = (p / t) * 100
        status = "✅" if p == t else "⚠️"
        print(f"{status} {category}: {p}/{t} ({percentage:.1f}%)")
        if p != t:
            all_passed = False
    
    print(f"\n=== Key Improvements ===")
    print("✅ Terms from filenames are automatically extracted")
    print("✅ Single technical terms are allowed by default")
    print("✅ Context-sensitive filtering for ambiguous terms")
    print("✅ Dynamic whitelist from document content")
    print("✅ Maintains security against non-automotive queries")
    
    # Show some extracted filename terms
    filename_terms = [term for term in domain_validator.document_whitelist 
                     if any(t in term for t in ['antenna', 'keyless', 'joyride', 'montaggio'])]
    
    print(f"\n=== Sample Filename Terms ===")
    print("Terms extracted from document filenames:")
    for term in filename_terms[:10]:
        print(f"  - {term}")
    
    if all_passed:
        print(f"\n🎉 ALL TESTS PASSED!")
        print("✅ The general solution works for any automotive term")
    else:
        print(f"\n⚠️  Some tests failed")
        print("❌ The solution may need further refinement")

if __name__ == "__main__":
    asyncio.run(test_general_solution())
