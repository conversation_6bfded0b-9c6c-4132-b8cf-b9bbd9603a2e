#!/usr/bin/env python3
"""
Final test for the improved document whitelist functionality
"""

import asyncio
import logging
from security_utils import domain_validator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_final_whitelist():
    """Test the final document whitelist functionality"""
    print("=== Final Document Whitelist Test ===")
    
    # Get current stats
    stats = domain_validator.get_whitelist_stats()
    print(f"Current whitelist size: {stats['whitelist_size']}")
    print(f"Last update: {stats['last_update']}")
    
    # Test various queries that should now work
    test_queries = [
        # Previously blocked terms that should now work
        ("Lubrificanti Silkolene", True, "Brand name from documents"),
        ("sistema Synerject", True, "Technical system from documents"),
        ("corpo farfallato", True, "Technical term from documents"),
        ("Symphony ST200", True, "Model name from documents"),
        ("euro 5 specifiche", True, "Standard specification"),
        
        # Terms that should still work (basic automotive)
        ("olio motore", True, "Basic automotive term"),
        ("filtro aria", True, "Basic automotive term"),
        ("cambio olio", True, "Basic automotive maintenance"),
        
        # Terms that should still be blocked
        ("come cucinare la pizza", False, "Non-automotive topic"),
        ("programmazione python", False, "Non-automotive topic"),
        ("previsioni del tempo", False, "Non-automotive topic"),
        
        # Edge cases
        ("Silkolene PRO 4", True, "Specific product from documents"),
        ("M4B ricalibrazione", True, "Technical procedure from documents"),
    ]
    
    print("\n=== Testing queries ===")
    passed = 0
    total = len(test_queries)
    
    for query, expected_allowed, description in test_queries:
        is_relevant, error = domain_validator.validate_domain_relevance(query)
        
        if is_relevant == expected_allowed:
            status = "✓ PASS"
            passed += 1
        else:
            status = "✗ FAIL"
        
        allowed_str = "ALLOWED" if is_relevant else "BLOCKED"
        expected_str = "should be ALLOWED" if expected_allowed else "should be BLOCKED"
        
        print(f"{status}: '{query}' -> {allowed_str} ({expected_str}) - {description}")
        if error and not is_relevant:
            print(f"      Error: {error}")
    
    print(f"\n=== Results ===")
    print(f"Passed: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! The whitelist system is working correctly.")
    else:
        print("⚠️  Some tests failed. The whitelist system may need further adjustments.")
    
    # Show some sample whitelist terms
    print(f"\n=== Sample whitelist terms ===")
    sample_terms = [term for term in domain_validator.document_whitelist if 'silkolene' in term or 'synerject' in term or 'symphony' in term][:10]
    for term in sample_terms:
        print(f"  - {term}")

if __name__ == "__main__":
    asyncio.run(test_final_whitelist())
