#!/usr/bin/env python3
"""
Test script for database integration
Verifies that the chatbot logging system works correctly
"""

import asyncio
import logging
import sys
from datetime import datetime
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from database_logger import DatabaseLogger, ConversationLog, get_database_logger, close_database_logger
from config import config

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_database_connection():
    """Test basic database connection"""
    print("🔍 Testing database connection...")
    
    try:
        db_logger = DatabaseLogger()
        await db_logger.initialize()
        print("✅ Database connection successful")
        await db_logger.close()
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

async def test_conversation_logging():
    """Test conversation logging functionality"""
    print("🔍 Testing conversation logging...")
    
    try:
        db_logger = await get_database_logger()
        
        # Create test conversation log
        test_log = ConversationLog(
            session_id="test_session_001",
            question="Come posso configurare il sistema di backup?",
            answer="Per configurare il backup, segui questi passaggi...",
            ip_address="*************",
            product="TestProduct",
            search_result_count=3,
            avg_relevance_score=0.8500,
            used_general_knowledge=False,
            response_time_ms=1250,
            confidence=0.9200,
            tool_executions={
                'tools_used': ['document_search', 'knowledge_retrieval'],
                'execution_time_ms': {'document_search': 800, 'knowledge_retrieval': 450},
                'total_execution_time_ms': 1250
            }
        )
        
        # Log conversation
        success = await db_logger.log_conversation(test_log)
        
        if success:
            print("✅ Conversation logging successful")
            return True
        else:
            print("❌ Conversation logging failed")
            return False
            
    except Exception as e:
        print(f"❌ Conversation logging test failed: {e}")
        return False

async def test_session_stats():
    """Test session statistics retrieval"""
    print("🔍 Testing session statistics...")
    
    try:
        db_logger = await get_database_logger()
        
        # Get stats for test session
        stats = await db_logger.get_session_stats("test_session_001")
        
        if stats:
            print("✅ Session statistics retrieved successfully")
            print(f"   Total queries: {stats['total_queries']}")
            print(f"   Avg response time: {stats['avg_response_time_ms']}ms")
            print(f"   Avg confidence: {stats['avg_confidence']}")
            return True
        else:
            print("⚠️ No session statistics found (this is normal for new sessions)")
            return True
            
    except Exception as e:
        print(f"❌ Session statistics test failed: {e}")
        return False

async def test_performance_metrics():
    """Test performance metrics retrieval"""
    print("🔍 Testing performance metrics...")
    
    try:
        db_logger = await get_database_logger()
        
        # Get performance metrics for last 24 hours
        metrics = await db_logger.get_performance_metrics(24)
        
        print("✅ Performance metrics retrieved successfully")
        print(f"   Total conversations: {metrics.get('total_conversations', 0)}")
        print(f"   Unique sessions: {metrics.get('unique_sessions', 0)}")
        print(f"   Products used: {metrics.get('products_used', 0)}")
        
        return True
            
    except Exception as e:
        print(f"❌ Performance metrics test failed: {e}")
        return False

async def test_recent_conversations():
    """Test recent conversations retrieval"""
    print("🔍 Testing recent conversations retrieval...")
    
    try:
        db_logger = await get_database_logger()
        
        # Get recent conversations
        conversations = await db_logger.get_recent_conversations(5)
        
        print(f"✅ Retrieved {len(conversations)} recent conversations")
        
        for conv in conversations[:2]:  # Show first 2
            print(f"   Session: {conv['session_id'][:12]}... | Product: {conv.get('product', 'N/A')} | Time: {conv['response_time_ms']}ms")
        
        return True
            
    except Exception as e:
        print(f"❌ Recent conversations test failed: {e}")
        return False

async def test_database_schema():
    """Test that the database schema exists"""
    print("🔍 Testing database schema...")
    
    try:
        db_logger = await get_database_logger()
        
        async with db_logger.get_connection() as conn:
            async with conn.cursor() as cursor:
                # Check if table exists
                await cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = %s AND table_name = 'chatbot_logs'
                """, (config.db_database,))
                
                result = await cursor.fetchone()
                
                if result[0] > 0:
                    print("✅ Database schema exists")
                    
                    # Check table structure
                    await cursor.execute("DESCRIBE chatbot_logs")
                    columns = await cursor.fetchall()
                    
                    expected_columns = [
                        'id', 'created_at', 'ip_address', 'session_id', 'product',
                        'question', 'answer', 'search_result_count', 'avg_relevance_score',
                        'used_general_knowledge', 'response_time_ms', 'confidence', 'tool_executions'
                    ]
                    
                    actual_columns = [col[0] for col in columns]
                    missing_columns = set(expected_columns) - set(actual_columns)
                    
                    if missing_columns:
                        print(f"⚠️ Missing columns: {missing_columns}")
                        return False
                    else:
                        print("✅ All required columns present")
                        return True
                else:
                    print("❌ Database table 'chatbot_logs' does not exist")
                    print("Please run: mysql -u prova -p < database/create_chatbot_logs_table.sql")
                    return False
                    
    except Exception as e:
        print(f"❌ Database schema test failed: {e}")
        return False

async def run_all_tests():
    """Run all database integration tests"""
    print("🚀 Starting Database Integration Tests")
    print("=" * 50)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Database Schema", test_database_schema),
        ("Conversation Logging", test_conversation_logging),
        ("Session Statistics", test_session_stats),
        ("Performance Metrics", test_performance_metrics),
        ("Recent Conversations", test_recent_conversations),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Database integration is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the configuration and database setup.")
    
    # Cleanup
    await close_database_logger()
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        sys.exit(1)
