#!/usr/bin/env python3
"""
Debug script to analyze no-link file processing
"""

import asyncio
import logging
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from document_processor import DocumentProcessor
from mcp_server import MCPResource, MCPServer
from mcp_tools import MCPToolHandler

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_nolink_processing():
    """Debug no-link file processing"""
    
    print("🔍 Debugging No-Link File Processing")
    print("=" * 50)
    
    # Initialize components
    doc_processor = DocumentProcessor()
    mcp_server = MCPServer()
    tool_handler = MCPToolHandler(mcp_server)
    
    # Define the no-link file
    nolink_file_path = Path("sorgenti/SYM/nolink/file-nolink.pdf")
    
    if not nolink_file_path.exists():
        print(f"❌ No-link file not found: {nolink_file_path}")
        return
    
    print(f"✅ Found no-link file: {nolink_file_path}")
    print(f"   File size: {nolink_file_path.stat().st_size} bytes")
    
    # Create MCPResource for no-link file
    nolink_resource = MCPResource(
        name="SYM/nolink/file-nolink",
        uri=f"file://{nolink_file_path.absolute()}",
        description="No-link technical document for SYM",
        metadata={
            'file_path': str(nolink_file_path.absolute()),
            'product': 'SYM',
            'category': 'nolink',
            'filename': 'file-nolink.pdf',
            'citation_required': False  # No-link files don't require citation
        }
    )
    
    print(f"\n📄 Processing no-link file...")
    
    # Process the no-link document
    processed_doc = await doc_processor.process_resource(nolink_resource)
    
    if not processed_doc:
        print("❌ Failed to process no-link document")
        return
    
    print(f"✅ Successfully processed no-link document:")
    print(f"   - Page count: {processed_doc.page_count}")
    print(f"   - Chunk count: {len(processed_doc.chunks)}")
    print(f"   - Table count: {len(processed_doc.tables)}")
    print(f"   - Image count: {len(processed_doc.images)}")
    print(f"   - Full text length: {len(processed_doc.full_text)} characters")
    
    # Show first few chunks
    print(f"\n📋 First 3 chunks from no-link file:")
    for i, chunk in enumerate(processed_doc.chunks[:3]):
        print(f"\n--- Chunk {i+1} (Page {chunk.page_number}) ---")
        preview = chunk.text[:200] + "..." if len(chunk.text) > 200 else chunk.text
        print(preview)
    
    # Show tables if any
    if processed_doc.tables:
        print(f"\n📊 Tables found in no-link file:")
        for i, table in enumerate(processed_doc.tables):
            print(f"\n--- Table {i+1} (Page {table.get('page', 'Unknown')}) ---")
            table_preview = table.get('content', '')[:300] + "..." if len(table.get('content', '')) > 300 else table.get('content', '')
            print(table_preview)
    
    # Test search functionality with no-link content
    print(f"\n🔍 Testing search functionality...")
    
    # Register the resource in MCP server
    mcp_server.resource_manager.register_resource(nolink_resource)
    
    # Test search with various terms
    search_terms = [
        "serraggio",
        "bulloni",
        "coppia",
        "nm",
        "kgf",
        "testa",
        "cilindro"
    ]
    
    for term in search_terms:
        print(f"\n--- Searching for: '{term}' ---")
        
        # Search in processed document
        results = await doc_processor.search_documents(term, "SYM")
        
        if results:
            print(f"   Found {len(results)} results")
            for i, result in enumerate(results[:2]):  # Show top 2 results
                filename = result.get('filename', 'Unknown')
                page = result.get('page', 'Unknown')
                score = result.get('relevance_score', 0)
                content_preview = result.get('content', '')[:150] + "..." if len(result.get('content', '')) > 150 else result.get('content', '')
                
                print(f"   Result {i+1}: {filename}, page {page} (score: {score:.3f})")
                print(f"   Content: {content_preview}")
        else:
            print("   No results found")
    
    # Test with MCP tool handler
    print(f"\n🛠️ Testing with MCP Tool Handler...")
    
    # Register all SYM resources
    await mcp_server.register_product_resources("SYM")
    
    # Test search through tool handler
    tool_result = await tool_handler.search_documents("serraggio bulloni", "SYM")
    
    if tool_result.success:
        print(f"✅ Tool search successful:")
        print(f"   Found {len(tool_result.data)} results")
        
        # Check if any results are from no-link files
        nolink_results = [r for r in tool_result.data if 'nolink' in r.get('filename', '').lower()]
        if nolink_results:
            print(f"   🎯 Found {len(nolink_results)} results from no-link files!")
            for result in nolink_results:
                print(f"      - {result.get('filename', 'Unknown')}, page {result.get('page', 'Unknown')}")
        else:
            print(f"   ⚠️ No results from no-link files found")
    else:
        print(f"❌ Tool search failed: {tool_result.error}")

if __name__ == "__main__":
    asyncio.run(debug_nolink_processing())
