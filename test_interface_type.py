#!/usr/bin/env python3
"""
Test script for interface_type column functionality
Tests CLI vs WEB interface type logging
"""

import asyncio
import logging
import sys
from datetime import datetime
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from database_logger import get_database_logger, close_database_logger
from session_manager import SessionManager
from mcp_server import MCPServer

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_cli_interface_logging():
    """Test CLI interface type logging"""
    print("🔍 Testing CLI interface type logging...")
    
    try:
        # Initialize components
        mcp_server = MCPServer()
        await mcp_server.start()
        session_manager = SessionManager(mcp_server)
        
        # Test CLI query
        print("  Simulating CLI query...")
        result = await session_manager.process_query_with_ip(
            "Test CLI interface query",
            "TestProduct",
            "127.0.0.1",
            "CLI"
        )
        
        await mcp_server.stop()
        print("✅ CLI interface logging test completed")
        return True
        
    except Exception as e:
        print(f"❌ CLI interface logging test failed: {e}")
        return False

async def test_web_interface_logging():
    """Test WEB interface type logging"""
    print("🔍 Testing WEB interface type logging...")
    
    try:
        # Initialize components
        mcp_server = MCPServer()
        await mcp_server.start()
        session_manager = SessionManager(mcp_server)
        
        # Test WEB query
        print("  Simulating WEB query...")
        result = await session_manager.process_query_with_ip(
            "Test WEB interface query",
            "TestProduct",
            "*************",
            "WEB"
        )
        
        await mcp_server.stop()
        print("✅ WEB interface logging test completed")
        return True
        
    except Exception as e:
        print(f"❌ WEB interface logging test failed: {e}")
        return False

async def verify_interface_types_in_database():
    """Verify that interface types are correctly stored in database"""
    print("🔍 Verifying interface types in database...")
    
    try:
        db_logger = await get_database_logger()
        
        # Get recent conversations with interface types
        async with db_logger.get_connection() as conn:
            async with conn.cursor() as cursor:
                query = """
                SELECT 
                    question, interface_type, ip_address, created_at
                FROM chatbot_logs 
                WHERE question LIKE '%Test%interface%'
                ORDER BY created_at DESC 
                LIMIT 10
                """
                
                await cursor.execute(query)
                results = await cursor.fetchall()
                
                if not results:
                    print("  No test conversations found")
                    return False
                
                print("  Recent test conversations:")
                cli_found = False
                web_found = False
                
                for row in results:
                    question = row[0][:40] + "..." if len(row[0]) > 40 else row[0]
                    interface_type = row[1]
                    ip_address = row[2]
                    created_at = row[3]
                    
                    print(f"    {interface_type}: {question} | IP: {ip_address}")
                    
                    if interface_type == 'CLI':
                        cli_found = True
                    elif interface_type == 'WEB':
                        web_found = True
                
                if cli_found and web_found:
                    print("✅ Both CLI and WEB interface types found in database")
                    return True
                else:
                    print(f"❌ Missing interface types - CLI: {cli_found}, WEB: {web_found}")
                    return False
        
    except Exception as e:
        print(f"❌ Database verification failed: {e}")
        return False

async def test_interface_type_statistics():
    """Test interface type statistics queries"""
    print("🔍 Testing interface type statistics...")
    
    try:
        db_logger = await get_database_logger()
        
        # Get interface type distribution
        async with db_logger.get_connection() as conn:
            async with conn.cursor() as cursor:
                query = """
                SELECT 
                    interface_type,
                    COUNT(*) as conversation_count,
                    AVG(response_time_ms) as avg_response_time,
                    AVG(confidence) as avg_confidence
                FROM chatbot_logs 
                WHERE created_at >= NOW() - INTERVAL 1 HOUR
                GROUP BY interface_type
                ORDER BY interface_type
                """
                
                await cursor.execute(query)
                results = await cursor.fetchall()
                
                print("  Interface type statistics (last hour):")
                print("  " + "-" * 60)
                print(f"  {'Type':<6} {'Count':<8} {'Avg Time':<12} {'Avg Confidence':<15}")
                print("  " + "-" * 60)
                
                for row in results:
                    interface_type = row[0]
                    count = row[1]
                    avg_time = f"{row[2]:.0f}ms" if row[2] else "N/A"
                    avg_conf = f"{row[3]:.2f}" if row[3] else "N/A"
                    
                    print(f"  {interface_type:<6} {count:<8} {avg_time:<12} {avg_conf:<15}")
                
                print("✅ Interface type statistics test completed")
                return True
        
    except Exception as e:
        print(f"❌ Interface type statistics test failed: {e}")
        return False

async def test_migration_compatibility():
    """Test that the migration script works correctly"""
    print("🔍 Testing migration compatibility...")
    
    try:
        db_logger = await get_database_logger()
        
        # Check if interface_type column exists
        async with db_logger.get_connection() as conn:
            async with conn.cursor() as cursor:
                query = """
                SELECT COLUMN_NAME, COLUMN_TYPE, COLUMN_DEFAULT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'softway_chat' 
                AND TABLE_NAME = 'chatbot_logs' 
                AND COLUMN_NAME = 'interface_type'
                """
                
                await cursor.execute(query)
                result = await cursor.fetchone()
                
                if result:
                    column_name = result[0]
                    column_type = result[1]
                    column_default = result[2]
                    
                    print(f"  Column found: {column_name}")
                    print(f"  Type: {column_type}")
                    print(f"  Default: {column_default}")
                    
                    # Verify it's an ENUM with correct values
                    if "enum('CLI','WEB')" in column_type.lower():
                        print("✅ interface_type column is correctly configured")
                        return True
                    else:
                        print(f"❌ interface_type column type is incorrect: {column_type}")
                        return False
                else:
                    print("❌ interface_type column not found")
                    print("Please run the migration script:")
                    print("mysql -u prova -p < database/migrate_add_interface_type.sql")
                    return False
        
    except Exception as e:
        print(f"❌ Migration compatibility test failed: {e}")
        return False

async def run_interface_type_tests():
    """Run all interface type tests"""
    print("🚀 Testing Interface Type Column")
    print("=" * 50)
    
    tests = [
        ("Migration Compatibility", test_migration_compatibility),
        ("CLI Interface Logging", test_cli_interface_logging),
        ("WEB Interface Logging", test_web_interface_logging),
        ("Database Verification", verify_interface_types_in_database),
        ("Interface Type Statistics", test_interface_type_statistics),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 INTERFACE TYPE TEST RESULTS")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Interface type column is working correctly!")
        print("\nFeatures verified:")
        print("✅ Column exists with correct ENUM type")
        print("✅ CLI interface logging works")
        print("✅ WEB interface logging works")
        print("✅ Database stores interface types correctly")
        print("✅ Statistics queries work with interface types")
    else:
        print("⚠️ Some interface type features need attention.")
        if passed == 0:
            print("\n💡 Quick fix:")
            print("Run: mysql -u prova -p < database/migrate_add_interface_type.sql")
    
    # Cleanup
    await close_database_logger()
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(run_interface_type_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        sys.exit(1)
