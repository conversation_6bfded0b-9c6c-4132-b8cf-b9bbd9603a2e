#!/usr/bin/env python3
"""
Test script for document whitelist functionality
"""

import asyncio
import logging
from pathlib import Path
from security_utils import domain_validator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_whitelist():
    """Test the document whitelist functionality"""
    print("=== Document Whitelist Test ===")
    
    # Get initial stats
    stats = domain_validator.get_whitelist_stats()
    print(f"Initial whitelist size: {stats['whitelist_size']}")
    print(f"Last update: {stats['last_update']}")
    print(f"Cache file exists: {stats['cache_file_exists']}")
    
    if stats['sample_terms']:
        print(f"Sample terms: {', '.join(stats['sample_terms'])}")
    
    # Force update
    print("\n=== Forcing whitelist update ===")
    await domain_validator.force_whitelist_update()
    
    # Get updated stats
    stats = domain_validator.get_whitelist_stats()
    print(f"Updated whitelist size: {stats['whitelist_size']}")
    print(f"Last update: {stats['last_update']}")
    
    if stats['sample_terms']:
        print(f"Sample terms: {', '.join(stats['sample_terms'])}")
    
    # Test some queries
    test_queries = [
        "Lubrificanti Silkolene",
        "olio motore Castrol",
        "filtro aria",
        "come cucinare la pizza",  # Should be blocked
        "Symphony ST200",
        "corpo farfallato",
        "sistema Synerject",
        "euro 5 specifiche"
    ]
    
    print("\n=== Testing queries ===")
    for query in test_queries:
        is_relevant, error = domain_validator.validate_domain_relevance(query)
        status = "✓ ALLOWED" if is_relevant else "✗ BLOCKED"
        print(f"{status}: '{query}' - {error or 'OK'}")

if __name__ == "__main__":
    asyncio.run(test_whitelist())
