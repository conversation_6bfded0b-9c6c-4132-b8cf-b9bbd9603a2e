# Utilities Directory

This directory contains reusable utility scripts and tools for the Softway Chatbot SYM system.

## 🛠️ Available Utilities

### 1. Cache Management (`cache_utils.py`)
Command-line tools for managing document processing cache.

**Usage:**
```bash
python utils/cache_utils.py stats          # Show cache statistics
python utils/cache_utils.py clear          # Clear all cache
python utils/cache_utils.py cleanup        # Clean expired entries
python utils/cache_utils.py optimize       # Optimize cache performance
```

**Features:**
- Cache statistics and monitoring
- Automatic cleanup of expired entries
- Cache size optimization
- Support for both file-based and Redis cache

### 2. Database Monitoring (`monitor_database.py`)
Real-time monitoring of chatbot conversation logs and analytics.

**Usage:**
```bash
python utils/monitor_database.py           # Continuous monitoring
python utils/monitor_database.py --once    # Single report
python utils/monitor_database.py --stats   # Show statistics only
```

**Features:**
- Real-time conversation monitoring
- Performance metrics tracking
- Interface type analytics (CLI vs Web)
- Database health monitoring

### 3. Performance Analysis (`optimize.py`)
System performance analysis and optimization recommendations.

**Usage:**
```bash
python utils/optimize.py                   # Full system analysis
python utils/optimize.py --quick          # Quick performance check
python utils/optimize.py --report         # Generate performance report
```

**Features:**
- System resource analysis
- Document processing optimization
- Configuration recommendations
- Performance bottleneck identification

### 4. Whitelist Management (`update_whitelist.py`)
Dynamic whitelist management for automotive terms.

**Usage:**
```bash
python utils/update_whitelist.py stats              # Show whitelist statistics
python utils/update_whitelist.py update             # Update whitelist (respects 24h interval)
python utils/update_whitelist.py force-update       # Force immediate update
python utils/update_whitelist.py test --query "term" # Test specific query
```

**Features:**
- Automatic term extraction from PDF documents
- Brand and technical term recognition
- Diagnostic code pattern matching
- Cache management for extracted terms

## 🔧 Integration

These utilities can be imported and used programmatically:

```python
from utils import CacheUtilities, DatabaseMonitor, PerformanceAnalyzer, WhitelistManager

# Cache management
cache_utils = CacheUtilities()
await cache_utils.show_stats()

# Database monitoring
db_monitor = DatabaseMonitor()
await db_monitor.show_recent_conversations()

# Performance analysis
analyzer = PerformanceAnalyzer()
await analyzer.analyze_system()

# Whitelist management
whitelist_mgr = WhitelistManager()
await whitelist_mgr.update_whitelist()
```

## 📋 Requirements

All utilities require the same dependencies as the main application:
- Python 3.8+
- Dependencies from `requirements.txt`
- Database connection (for database utilities)
- PDF documents in `sorgenti/` (for whitelist utilities)

## 🚀 Maintenance

These utilities are designed to be:
- **Self-contained**: Each utility can run independently
- **Reusable**: Can be imported and used in other scripts
- **Configurable**: Use the same configuration system as the main app
- **Robust**: Include proper error handling and logging

## 📊 Monitoring Schedule

Recommended usage schedule:
- **Cache cleanup**: Daily via cron job
- **Database monitoring**: Continuous or hourly
- **Performance analysis**: Weekly or when issues arise
- **Whitelist updates**: Automatic (24h interval) or manual as needed

## 🔒 Security

All utilities respect the same security measures as the main application:
- Environment variable configuration
- Safe file handling
- Input validation
- Secure database connections
