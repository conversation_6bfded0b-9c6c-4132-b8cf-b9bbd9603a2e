#!/usr/bin/env python3
"""
Database Monitor Script
Real-time monitoring of chatbot conversation logs
"""

import asyncio
import sys
from datetime import datetime
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from database_logger import get_database_logger, close_database_logger

async def show_recent_conversations(limit=10):
    """Show recent conversations with all relevant fields"""
    try:
        db_logger = await get_database_logger()
        conversations = await db_logger.get_recent_conversations(limit)
        
        if not conversations:
            print("No conversations found in database.")
            return
        
        print(f"\n📋 Last {len(conversations)} Conversations")
        print("=" * 100)
        
        for i, conv in enumerate(conversations, 1):
            created_at = conv.get('created_at', 'Unknown')
            session_id = conv.get('session_id', 'Unknown')[:12] + "..."
            ip_address = conv.get('ip_address', 'NULL')
            product = conv.get('product', 'NULL')
            interface_type = conv.get('interface_type', 'NULL')
            question = conv.get('question', '')[:60] + "..." if len(conv.get('question', '')) > 60 else conv.get('question', '')
            search_count = conv.get('search_result_count', 'NULL')
            confidence = conv.get('confidence', 'NULL')
            response_time = conv.get('response_time_ms', 'NULL')
            used_general = conv.get('used_general_knowledge', 'NULL')

            print(f"\n{i}. [{created_at}] Session: {session_id}")
            print(f"   Interface: {interface_type} | IP: {ip_address} | Product: {product}")
            print(f"   Question: {question}")
            print(f"   Search Results: {search_count} | Confidence: {confidence}")
            print(f"   Response Time: {response_time}ms | General Knowledge: {used_general}")
        
    except Exception as e:
        print(f"Error showing recent conversations: {e}")

async def show_session_stats():
    """Show statistics by session"""
    try:
        db_logger = await get_database_logger()
        
        async with db_logger.get_connection() as conn:
            async with conn.cursor() as cursor:
                query = """
                SELECT 
                    session_id,
                    COUNT(*) as query_count,
                    AVG(response_time_ms) as avg_response_time,
                    AVG(confidence) as avg_confidence,
                    AVG(search_result_count) as avg_search_results,
                    SUM(CASE WHEN used_general_knowledge = 1 THEN 1 ELSE 0 END) as general_knowledge_count,
                    MIN(created_at) as session_start,
                    MAX(created_at) as last_activity
                FROM chatbot_logs 
                WHERE created_at >= NOW() - INTERVAL 24 HOUR
                GROUP BY session_id
                ORDER BY last_activity DESC
                LIMIT 10
                """
                
                await cursor.execute(query)
                results = await cursor.fetchall()
                
                if not results:
                    print("No session data found for the last 24 hours.")
                    return
                
                print(f"\n📊 Session Statistics (Last 24 Hours)")
                print("=" * 120)
                print(f"{'Session ID':<15} {'Queries':<8} {'Avg Time':<10} {'Avg Conf':<10} {'Avg Docs':<10} {'Gen Know':<10} {'Duration':<15}")
                print("-" * 120)
                
                for row in results:
                    session_id = row[0][:12] + "..."
                    query_count = row[1]
                    avg_time = f"{row[2]:.0f}ms" if row[2] else "N/A"
                    avg_conf = f"{row[3]:.2f}" if row[3] else "N/A"
                    avg_docs = f"{row[4]:.1f}" if row[4] else "N/A"
                    gen_know = f"{row[5]}/{query_count}"
                    
                    # Calculate duration
                    if row[6] and row[7]:
                        duration = row[7] - row[6]
                        duration_str = str(duration).split('.')[0]  # Remove microseconds
                    else:
                        duration_str = "N/A"
                    
                    print(f"{session_id:<15} {query_count:<8} {avg_time:<10} {avg_conf:<10} {avg_docs:<10} {gen_know:<10} {duration_str:<15}")
        
    except Exception as e:
        print(f"Error showing session stats: {e}")

async def show_performance_metrics():
    """Show overall performance metrics"""
    try:
        db_logger = await get_database_logger()
        metrics = await db_logger.get_performance_metrics(24)
        
        if not metrics:
            print("No performance data available.")
            return
        
        print(f"\n📈 Performance Metrics (Last 24 Hours)")
        print("=" * 60)
        print(f"Total Conversations: {metrics.get('total_conversations', 0)}")
        print(f"CLI Conversations: {metrics.get('cli_conversations', 0)}")
        print(f"WEB Conversations: {metrics.get('web_conversations', 0)}")
        print(f"Unique Sessions: {metrics.get('unique_sessions', 0)}")
        print(f"Products Used: {metrics.get('products_used', 0)}")
        print(f"Avg Response Time: {metrics.get('avg_response_time_ms', 0):.0f}ms")
        print(f"Avg Confidence: {metrics.get('avg_confidence', 0):.2f}")
        print(f"Avg Search Results: {metrics.get('avg_search_results', 0):.1f}")
        print(f"General Knowledge Usage: {metrics.get('general_knowledge_percentage', 0):.1f}%")
        
    except Exception as e:
        print(f"Error showing performance metrics: {e}")

async def show_ip_distribution():
    """Show distribution of IP addresses"""
    try:
        db_logger = await get_database_logger()
        
        async with db_logger.get_connection() as conn:
            async with conn.cursor() as cursor:
                query = """
                SELECT 
                    ip_address,
                    COUNT(*) as conversation_count,
                    MIN(created_at) as first_seen,
                    MAX(created_at) as last_seen
                FROM chatbot_logs 
                WHERE created_at >= NOW() - INTERVAL 24 HOUR
                GROUP BY ip_address
                ORDER BY conversation_count DESC
                LIMIT 10
                """
                
                await cursor.execute(query)
                results = await cursor.fetchall()
                
                if not results:
                    print("No IP data found for the last 24 hours.")
                    return
                
                print(f"\n🌐 IP Address Distribution (Last 24 Hours)")
                print("=" * 80)
                print(f"{'IP Address':<20} {'Conversations':<15} {'First Seen':<20} {'Last Seen':<20}")
                print("-" * 80)
                
                for row in results:
                    ip = row[0] or "NULL"
                    count = row[1]
                    first_seen = row[2].strftime('%Y-%m-%d %H:%M:%S') if row[2] else "N/A"
                    last_seen = row[3].strftime('%Y-%m-%d %H:%M:%S') if row[3] else "N/A"
                    
                    print(f"{ip:<20} {count:<15} {first_seen:<20} {last_seen:<20}")
        
    except Exception as e:
        print(f"Error showing IP distribution: {e}")

async def monitor_database():
    """Main monitoring function"""
    print("🔍 Chatbot Database Monitor")
    print("=" * 50)
    print("Monitoring chatbot conversation logs...")
    print("Press Ctrl+C to exit")
    
    try:
        while True:
            print(f"\n🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            await show_recent_conversations(5)
            await show_session_stats()
            await show_performance_metrics()
            await show_ip_distribution()
            
            print("\n" + "="*100)
            print("Refreshing in 30 seconds... (Press Ctrl+C to exit)")
            
            await asyncio.sleep(30)
            
    except KeyboardInterrupt:
        print("\n👋 Monitoring stopped by user")
    except Exception as e:
        print(f"\n❌ Monitoring error: {e}")
    finally:
        await close_database_logger()

async def show_single_report():
    """Show a single report and exit"""
    print("📊 Chatbot Database Report")
    print("=" * 50)
    
    try:
        await show_recent_conversations(10)
        await show_session_stats()
        await show_performance_metrics()
        await show_ip_distribution()
        
    except Exception as e:
        print(f"❌ Report generation failed: {e}")
    finally:
        await close_database_logger()

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Monitor chatbot database logs")
    parser.add_argument("--once", action="store_true", help="Show report once and exit")
    parser.add_argument("--recent", type=int, default=10, help="Number of recent conversations to show")
    
    args = parser.parse_args()
    
    try:
        if args.once:
            asyncio.run(show_single_report())
        else:
            asyncio.run(monitor_database())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
