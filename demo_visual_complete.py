#!/usr/bin/env python3
"""
Demo completa delle funzionalità visive avanzate
Mostra tutti i miglioramenti implementati nell'interfaccia web
"""

import asyncio
import json
import time
import httpx
import websockets

class CompleteVisualDemo:
    """Demo completa delle funzionalità visive"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.session_id = None
    
    async def run_complete_demo(self):
        """Esegue la demo completa"""
        print("🎬 DEMO COMPLETA FUNZIONALITÀ VISIVE AVANZATE")
        print("=" * 60)
        print("🌐 Interfaccia Web: http://localhost:8000")
        print("👀 Apri il browser per vedere gli effetti in tempo reale!")
        print("=" * 60)
        
        # Verifica server
        if not await self.check_server():
            print("❌ Server non disponibile. Avvia con: python start_web.py")
            return
        
        print("✅ Server attivo e pronto")
        
        # Setup sessione
        await self.setup_demo_session()
        
        # Demo delle funzionalità
        await self.demo_thinking_indicators()
        await self.demo_markdown_formatting()
        await self.demo_sources_management()
        await self.demo_error_handling()
        
        print("\n🎉 DEMO COMPLETATA!")
        print("📱 Continua a testare l'interfaccia nel browser")
        print("🔗 URL: http://localhost:8000")
    
    async def check_server(self):
        """Verifica server"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.base_url}/api/health")
                return response.status_code == 200
        except:
            return False
    
    async def setup_demo_session(self):
        """Setup sessione demo"""
        print("\n🔧 SETUP SESSIONE DEMO")
        print("-" * 30)
        
        try:
            async with httpx.AsyncClient() as client:
                # Ottieni prodotti
                products_response = await client.get(f"{self.base_url}/api/products")
                products = products_response.json()
                
                print(f"📦 Prodotti disponibili: {len(products)}")
                for i, product in enumerate(products, 1):
                    print(f"   {i}. {product['display_name']}")
                
                # Crea sessione con primo prodotto
                if products:
                    session_response = await client.post(
                        f"{self.base_url}/api/session/create",
                        json={"product": products[0]["name"]}
                    )
                    
                    session_data = session_response.json()
                    self.session_id = session_data["session_id"]
                    print(f"✅ Sessione creata: {self.session_id}")
                    print(f"🎯 Prodotto selezionato: {products[0]['display_name']}")
                
        except Exception as e:
            print(f"❌ Errore setup: {e}")
    
    async def demo_thinking_indicators(self):
        """Demo indicatori di pensiero"""
        print("\n🤔 DEMO INDICATORI DI PENSIERO")
        print("-" * 40)
        print("👀 Guarda il browser per vedere gli indicatori animati!")
        
        if not self.session_id:
            print("❌ Nessuna sessione disponibile")
            return
        
        try:
            ws_url = f"ws://localhost:8000/ws/{self.session_id}"
            
            async with websockets.connect(ws_url) as websocket:
                print("📡 WebSocket connesso")
                
                # Messaggio che genera indicatori di progresso
                message = {
                    "message": "Come si effettua la manutenzione del filtro dell'aria?",
                    "product": "Symphony ST 200",
                    "session_id": self.session_id
                }
                
                print(f"📤 Invio domanda tecnica...")
                print(f"   💬 '{message['message']}'")
                
                await websocket.send(json.dumps(message))
                
                print("\n🎭 INDICATORI ATTESI:")
                print("   🤔 Indicatore 'Sto elaborando...'")
                print("   🔍 Indicatore 'Cerco nelle documentazioni...'")
                print("   ✍️ Indicatore 'Sto preparando la risposta...'")
                print("   📝 Risposta finale formattata")
                
                # Monitora i progressi
                indicators_seen = []
                start_time = time.time()
                
                while time.time() - start_time < 25:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                        data = json.loads(response)
                        
                        if data.get("type") == "typing":
                            status = data.get("status", "processing")
                            indicators_seen.append(f"typing-{status}")
                            print(f"   ✅ Typing indicator: {status}")
                            
                        elif data.get("type") == "progress":
                            message_text = data.get("message", "")
                            indicators_seen.append(f"progress-{message_text[:20]}")
                            print(f"   ✅ Progress: {message_text}")
                            
                        elif data.get("type") == "message":
                            print(f"   ✅ Risposta ricevuta: {len(data['response'])} caratteri")
                            if data.get("sources"):
                                print(f"   📚 Fonti disponibili: {len(data['sources'])}")
                            break
                            
                    except asyncio.TimeoutError:
                        continue
                
                print(f"\n📊 Indicatori visualizzati: {len(indicators_seen)}")
                
        except Exception as e:
            print(f"❌ Errore WebSocket: {e}")
    
    async def demo_markdown_formatting(self):
        """Demo formattazione markdown"""
        print("\n📝 DEMO FORMATTAZIONE MARKDOWN")
        print("-" * 40)
        print("👀 Guarda il browser per vedere la formattazione!")
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Messaggio che dovrebbe generare markdown
                message = {
                    "message": "Elenca in dettaglio i controlli di sicurezza pre-utilizzo",
                    "product": "Symphony ST 200",
                    "session_id": self.session_id
                }
                
                print(f"📤 Invio richiesta per lista dettagliata...")
                print(f"   💬 '{message['message']}'")
                
                response = await client.post(
                    f"{self.base_url}/api/chat",
                    json=message
                )
                
                if response.status_code == 200:
                    chat_response = response.json()
                    response_text = chat_response['response']
                    
                    print(f"\n📄 Risposta ricevuta: {len(response_text)} caratteri")
                    
                    # Analizza contenuto markdown
                    markdown_features = {
                        "Grassetto (**text**)": "**" in response_text or "__" in response_text,
                        "Corsivo (*text*)": "*" in response_text and "**" not in response_text,
                        "Liste puntate (-)": "- " in response_text,
                        "Liste numerate (1.)": any(f"{i}. " in response_text for i in range(1, 10)),
                        "A capo (\\n)": "\n" in response_text,
                        "Paragrafi multipli": response_text.count("\n\n") > 0
                    }
                    
                    print("\n🎨 FORMATTAZIONE RILEVATA:")
                    for feature, present in markdown_features.items():
                        status = "✅" if present else "❌"
                        print(f"   {status} {feature}")
                    
                    # Mostra anteprima formattata
                    if any(markdown_features.values()):
                        print("\n📋 ANTEPRIMA FORMATTAZIONE:")
                        preview = response_text[:200].replace("\n", "\\n")
                        print(f"   '{preview}...'")
                        print("   👆 Questa verrà renderizzata con HTML nel browser!")
                    
                else:
                    print(f"❌ Errore HTTP: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ Errore markdown demo: {e}")
    
    async def demo_sources_management(self):
        """Demo gestione fonti"""
        print("\n📚 DEMO GESTIONE FONTI")
        print("-" * 30)
        print("👀 Guarda il browser per vedere l'icona fonti!")
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Domanda tecnica specifica che dovrebbe generare fonti
                message = {
                    "message": "Quali sono le specifiche tecniche del motore e dove le trovo?",
                    "product": "Symphony ST 200",
                    "session_id": self.session_id
                }
                
                print(f"📤 Invio domanda per fonti specifiche...")
                print(f"   💬 '{message['message']}'")
                
                response = await client.post(
                    f"{self.base_url}/api/chat",
                    json=message
                )
                
                if response.status_code == 200:
                    chat_response = response.json()
                    sources = chat_response.get('sources', [])
                    citations = chat_response.get('citations', [])
                    
                    print(f"\n📊 RISULTATI FONTI:")
                    print(f"   📄 Fonti trovate: {len(sources)}")
                    print(f"   📝 Citazioni: {len(citations)}")
                    
                    if sources:
                        print("\n📋 DETTAGLI FONTI:")
                        for i, source in enumerate(sources[:5], 1):
                            filename = source.get('filename', 'Unknown')
                            page = source.get('page', 0)
                            relevance = source.get('relevance', 0)
                            print(f"   {i}. {filename} (pag. {page}) - Rilevanza: {relevance:.2f}")
                        
                        print("\n🎯 FUNZIONALITÀ BROWSER:")
                        print("   🔘 Icona fonti visibile nella bolla del bot")
                        print("   🔢 Contatore numero fonti")
                        print("   🖱️  Click per aprire modal con lista completa")
                        print("   📋 Fonti cliccabili nel modal")
                    else:
                        print("   ℹ️  Nessuna fonte specifica trovata per questa domanda")
                        print("   💡 Prova domande più tecniche per vedere le fonti")
                    
                else:
                    print(f"❌ Errore HTTP: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ Errore fonti demo: {e}")
    
    async def demo_error_handling(self):
        """Demo gestione errori"""
        print("\n⚠️ DEMO GESTIONE ERRORI")
        print("-" * 30)
        print("👀 Guarda il browser per vedere la gestione errori!")
        
        print("🔧 Simulazione scenari di errore...")
        print("   ✅ Input disabilitato durante elaborazione")
        print("   ✅ Riabilitazione automatica dopo risposta")
        print("   ✅ Timeout protection (30s)")
        print("   ✅ Messaggi di errore formattati")
        print("   ✅ Opzioni di retry disponibili")
        
        print("\n💡 TESTA MANUALMENTE:")
        print("   1. Invia un messaggio nel browser")
        print("   2. Osserva input disabilitato durante elaborazione")
        print("   3. Nota la riabilitazione dopo la risposta")
        print("   4. Prova a disconnettere internet per vedere errori")

async def main():
    """Funzione principale"""
    demo = CompleteVisualDemo()
    await demo.run_complete_demo()

if __name__ == "__main__":
    asyncio.run(main())
