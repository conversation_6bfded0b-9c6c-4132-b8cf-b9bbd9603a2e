#!/usr/bin/env python3
"""
Database Setup Script
Installs dependencies and sets up the database for chatbot logging
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a shell command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"   Error: {e.stderr.strip()}")
        return False

def check_mysql_connection():
    """Check if MySQL is accessible with the configured credentials"""
    print("🔍 Checking MySQL connection...")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    db_host = os.getenv('DB_HOST', 'localhost')
    db_port = os.getenv('DB_PORT', '3306')
    db_user = os.getenv('DB_USER', 'prova')
    db_password = os.getenv('DB_PASSWORD', 'prova')
    db_database = os.getenv('DB_DATABASE', 'softway_chat')
    
    # Test connection
    mysql_cmd = f"mysql -h {db_host} -P {db_port} -u {db_user} -p{db_password} -e 'SELECT 1;'"
    
    try:
        result = subprocess.run(mysql_cmd, shell=True, check=True, capture_output=True, text=True)
        print("✅ MySQL connection successful")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ MySQL connection failed")
        print(f"   Error: {e.stderr.strip()}")
        print(f"   Please check your MySQL credentials in .env file:")
        print(f"   DB_HOST={db_host}")
        print(f"   DB_PORT={db_port}")
        print(f"   DB_USER={db_user}")
        print(f"   DB_DATABASE={db_database}")
        return False

def install_dependencies():
    """Install required Python dependencies"""
    print("📦 Installing Python dependencies...")
    
    # Check if we're in a virtual environment
    if sys.prefix == sys.base_prefix:
        print("⚠️ Warning: Not in a virtual environment")
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            print("Aborted. Please activate a virtual environment first.")
            return False
    
    # Install dependencies
    return run_command(
        f"{sys.executable} -m pip install aiomysql PyMySQL",
        "Installing MySQL dependencies"
    )

def create_database_schema():
    """Create the database schema"""
    print("🗄️ Creating database schema...")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    db_host = os.getenv('DB_HOST', 'localhost')
    db_port = os.getenv('DB_PORT', '3306')
    db_user = os.getenv('DB_USER', 'prova')
    db_password = os.getenv('DB_PASSWORD', 'prova')
    
    # Check if SQL file exists
    sql_file = Path("database/create_chatbot_logs_table.sql")
    if not sql_file.exists():
        print(f"❌ SQL file not found: {sql_file}")
        return False
    
    # Execute SQL file
    mysql_cmd = f"mysql -h {db_host} -P {db_port} -u {db_user} -p{db_password} < {sql_file}"
    
    return run_command(mysql_cmd, "Creating database schema")

def run_integration_tests():
    """Run the database integration tests"""
    print("🧪 Running integration tests...")
    
    return run_command(
        f"{sys.executable} test_database_integration.py",
        "Running database integration tests"
    )

def main():
    """Main setup function"""
    print("🚀 Database Setup for Chatbot Logging")
    print("=" * 50)
    
    # Check if .env file exists
    if not Path(".env").exists():
        print("❌ .env file not found!")
        print("Please create a .env file with database configuration.")
        print("Example:")
        print("DB_HOST=localhost")
        print("DB_PORT=3306")
        print("DB_DATABASE=softway_chat")
        print("DB_USER=prova")
        print("DB_PASSWORD=prova")
        return False
    
    steps = [
        ("Install Dependencies", install_dependencies),
        ("Check MySQL Connection", check_mysql_connection),
        ("Create Database Schema", create_database_schema),
        ("Run Integration Tests", run_integration_tests),
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}")
        print("-" * 30)
        
        if not step_func():
            print(f"\n❌ Setup failed at step: {step_name}")
            print("Please fix the issues above and try again.")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 DATABASE SETUP COMPLETE!")
    print("=" * 50)
    print("✅ All dependencies installed")
    print("✅ Database schema created")
    print("✅ Integration tests passed")
    print("\nYour chatbot is now ready to log conversations to the database!")
    print("\nNext steps:")
    print("1. Start the CLI: python main.py")
    print("2. Start the web server: python start_web.py")
    print("3. Monitor logs in the database using the provided SQL queries")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)
