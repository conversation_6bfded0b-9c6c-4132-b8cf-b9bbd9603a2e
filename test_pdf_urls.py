#!/usr/bin/env python3
"""
Test script for PDF URL access with product names containing spaces
Regression test to ensure PDF URLs work correctly
"""

import asyncio
import aiohttp
import sys
from pathlib import Path
import urllib.parse

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from config import config

async def test_pdf_url_access():
    """Test PDF URL access with various product names"""
    print("🔍 Testing PDF URL Access")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000"
    
    # Test cases: (product_name, filename, should_work, description)
    test_cases = [
        (
            "Symphony ST 200", 
            "Manuale officina Symphony ST 200 E5.pdf",
            True,
            "Product with spaces"
        ),
        (
            "Joyride300",
            "file-nolink.pdf",  # This should exist in nolink folder
            True,
            "Product without spaces"
        ),
        (
            "NonExistent Product",
            "test.pdf",
            False,
            "Non-existent product"
        ),
        (
            "Symphony ST 200",
            "nonexistent.pdf",
            False,
            "Non-existent file"
        ),
        (
            "../etc/passwd",
            "test.pdf",
            False,
            "Path traversal attempt"
        ),
    ]
    
    async with aiohttp.ClientSession() as session:
        passed = 0
        total = len(test_cases)
        
        for product, filename, should_work, description in test_cases:
            print(f"\n📋 Testing: {description}")
            
            # URL encode the product name and filename
            encoded_product = urllib.parse.quote(product)
            encoded_filename = urllib.parse.quote(filename)
            
            url = f"{base_url}/api/pdf/{encoded_product}/{encoded_filename}"
            print(f"   URL: {url}")
            
            try:
                async with session.get(url) as response:
                    status_code = response.status
                    
                    if should_work:
                        if status_code == 200:
                            print(f"   ✅ PASS - Got 200 OK")
                            passed += 1
                        else:
                            print(f"   ❌ FAIL - Expected 200, got {status_code}")
                            if status_code == 400:
                                error_text = await response.text()
                                print(f"   Error: {error_text}")
                    else:
                        if status_code in [400, 404]:
                            print(f"   ✅ PASS - Correctly rejected with {status_code}")
                            passed += 1
                        else:
                            print(f"   ❌ FAIL - Expected 400/404, got {status_code}")
                            
            except Exception as e:
                print(f"   ❌ EXCEPTION: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"📊 PDF URL TEST RESULTS")
    print(f"=" * 50)
    print(f"Passed: {passed}/{total} tests")
    
    return passed == total

async def test_specific_symphony_urls():
    """Test specific Symphony ST 200 URLs that were problematic"""
    print(f"\n🔍 Testing Specific Symphony ST 200 URLs")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000"
    
    # These are the actual URLs that should work
    test_urls = [
        "/api/pdf/Symphony%20ST%20200/Manuale%20officina%20Symphony%20ST%20200%20E5.pdf",
        "/api/pdf/Symphony%20ST%20200/Manuale%20officina%20Symphony%20ST%20200%20E5.pdf#page=4",
        "/api/pdf/Symphony%20ST%20200/Codici%20guasto%20E5-E5%2B%20e%20ABS.pdf",
        "/api/pdf/Symphony%20ST%20200/Symphony%20ST125%20200%20E5%20Manuale%20uso%20e%20manutenzione.pdf",
    ]
    
    async with aiohttp.ClientSession() as session:
        passed = 0
        total = len(test_urls)
        
        for test_url in test_urls:
            full_url = base_url + test_url
            print(f"\n📋 Testing: {test_url}")
            
            try:
                async with session.get(full_url) as response:
                    status_code = response.status
                    
                    if status_code == 200:
                        content_type = response.headers.get('content-type', '')
                        print(f"   ✅ PASS - 200 OK, Content-Type: {content_type}")
                        passed += 1
                    else:
                        print(f"   ❌ FAIL - Status: {status_code}")
                        if status_code == 400:
                            error_text = await response.text()
                            print(f"   Error: {error_text}")
                            
            except Exception as e:
                print(f"   ❌ EXCEPTION: {e}")
    
    print(f"\nPassed: {passed}/{total} Symphony URLs")
    return passed == total

async def test_url_encoding_variations():
    """Test different URL encoding variations"""
    print(f"\n🔍 Testing URL Encoding Variations")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000"
    product = "Symphony ST 200"
    filename = "Manuale officina Symphony ST 200 E5.pdf"
    
    # Different encoding variations
    variations = [
        (urllib.parse.quote(product), urllib.parse.quote(filename), "Standard encoding"),
        (urllib.parse.quote_plus(product), urllib.parse.quote_plus(filename), "Plus encoding"),
        (product.replace(" ", "%20"), filename.replace(" ", "%20"), "Manual %20 encoding"),
    ]
    
    async with aiohttp.ClientSession() as session:
        passed = 0
        total = len(variations)
        
        for encoded_product, encoded_filename, description in variations:
            print(f"\n📋 Testing: {description}")
            
            url = f"{base_url}/api/pdf/{encoded_product}/{encoded_filename}"
            print(f"   URL: {url}")
            
            try:
                async with session.get(url) as response:
                    status_code = response.status
                    
                    if status_code == 200:
                        print(f"   ✅ PASS - 200 OK")
                        passed += 1
                    else:
                        print(f"   ❌ FAIL - Status: {status_code}")
                        
            except Exception as e:
                print(f"   ❌ EXCEPTION: {e}")
    
    print(f"\nPassed: {passed}/{total} encoding variations")
    return passed == total

async def main():
    """Main test function"""
    print("🚀 PDF URL Access Tests")
    print("=" * 60)
    
    try:
        # Test if server is running
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get("http://127.0.0.1:8000/") as response:
                    if response.status != 200:
                        print("⚠️ Web server may not be running properly")
            except Exception:
                print("❌ Web server is not running!")
                print("Please start it with: python start_web.py")
                return False
        
        # Run all tests
        basic_ok = await test_pdf_url_access()
        symphony_ok = await test_specific_symphony_urls()
        encoding_ok = await test_url_encoding_variations()
        
        print(f"\n" + "=" * 60)
        print("📊 OVERALL TEST RESULTS")
        print("=" * 60)
        
        results = [
            ("Basic PDF URL Access", basic_ok),
            ("Symphony ST 200 URLs", symphony_ok),
            ("URL Encoding Variations", encoding_ok),
        ]
        
        all_passed = True
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
            if not result:
                all_passed = False
        
        if all_passed:
            print(f"\n🎉 All PDF URL tests passed!")
            print(f"\nThe original problematic URL now works:")
            print(f"http://127.0.0.1:8000/api/pdf/Symphony%20ST%20200/Manuale%20officina%20Symphony%20ST%20200%20E5.pdf#page=4")
        else:
            print(f"\n⚠️ Some PDF URL tests failed.")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
