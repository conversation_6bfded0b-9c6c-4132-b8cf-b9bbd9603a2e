#!/usr/bin/env python3
"""
Demo script for the web interface
Shows the capabilities of the chatbot web interface
"""

import asyncio
import json
import time
from pathlib import Path
import httpx
import websockets

class WebInterfaceDemo:
    """Demo class for showcasing web interface features"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session_id = None
        
    async def run_demo(self):
        """Run the complete demo"""
        print("🎬 DEMO: Chatbot Web Interface")
        print("=" * 50)
        
        # Check if server is running
        if not await self.check_server():
            print("❌ Server not running. Please start with: python start_web.py")
            return
        
        print("✅ Server is running!")
        print(f"🌐 Web interface available at: {self.base_url}")
        print()
        
        # Show available products
        await self.show_products()
        
        # Create a session
        await self.create_demo_session()
        
        # Demo WebSocket chat
        await self.demo_websocket_chat()
        
        # Demo REST API
        await self.demo_rest_api()
        
        print("\n🎉 Demo completed!")
        print(f"👉 Open your browser and visit: {self.base_url}")
        print("   Try asking technical questions about your products!")
        
    async def check_server(self):
        """Check if the server is running"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.base_url}/api/health")
                return response.status_code == 200
        except:
            return False
    
    async def show_products(self):
        """Show available products"""
        print("📦 Available Products:")
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/api/products")
                products = response.json()
                
                for i, product in enumerate(products, 1):
                    print(f"   {i}. {product['display_name']}")
                
                print()
                return products
        except Exception as e:
            print(f"   Error loading products: {e}")
            return []
    
    async def create_demo_session(self):
        """Create a demo session"""
        print("🔧 Creating Demo Session...")
        try:
            async with httpx.AsyncClient() as client:
                # Get first available product
                products_response = await client.get(f"{self.base_url}/api/products")
                products = products_response.json()
                
                if not products:
                    print("   No products available")
                    return
                
                # Create session with first product
                session_response = await client.post(
                    f"{self.base_url}/api/session/create",
                    json={"product": products[0]["name"]}
                )
                
                session_data = session_response.json()
                self.session_id = session_data["session_id"]
                
                print(f"   ✅ Session created: {self.session_id}")
                print(f"   📋 Product: {products[0]['display_name']}")
                print()
                
        except Exception as e:
            print(f"   ❌ Error creating session: {e}")
    
    async def demo_websocket_chat(self):
        """Demo WebSocket chat functionality"""
        if not self.session_id:
            print("❌ No session available for WebSocket demo")
            return
        
        print("💬 WebSocket Chat Demo:")
        print("   Connecting to WebSocket...")
        
        try:
            ws_url = f"ws://localhost:8000/ws/{self.session_id}"
            
            async with websockets.connect(ws_url) as websocket:
                print("   ✅ WebSocket connected!")
                
                # Send a test message
                test_message = {
                    "message": "Come posso controllare il livello dell'olio?",
                    "product": "Symphony ST 200",
                    "session_id": self.session_id
                }
                
                print(f"   📤 Sending: {test_message['message']}")
                await websocket.send(json.dumps(test_message))
                
                # Wait for responses
                response_count = 0
                while response_count < 2:  # Expect typing + message
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                        data = json.loads(response)
                        
                        if data.get("type") == "typing":
                            print("   ⏳ Bot is typing...")
                        elif data.get("type") == "message":
                            print(f"   📥 Response: {data['response'][:100]}...")
                            if data.get("sources"):
                                print(f"   📚 Sources: {len(data['sources'])} documents")
                            break
                        
                        response_count += 1
                        
                    except asyncio.TimeoutError:
                        print("   ⏰ Response timeout")
                        break
                
                print("   ✅ WebSocket demo completed!")
                print()
                
        except Exception as e:
            print(f"   ❌ WebSocket error: {e}")
    
    async def demo_rest_api(self):
        """Demo REST API functionality"""
        print("🔗 REST API Demo:")
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Test chat endpoint
                chat_data = {
                    "message": "Quali sono i controlli di manutenzione periodica?",
                    "product": "Symphony ST 200",
                    "session_id": self.session_id
                }
                
                print(f"   📤 Sending: {chat_data['message']}")
                print("   ⏳ Processing...")
                
                response = await client.post(
                    f"{self.base_url}/api/chat",
                    json=chat_data
                )
                
                if response.status_code == 200:
                    chat_response = response.json()
                    print(f"   📥 Response: {chat_response['response'][:100]}...")
                    print(f"   📚 Citations: {len(chat_response['citations'])} found")
                    print(f"   📄 Sources: {len(chat_response['sources'])} documents")
                else:
                    print(f"   ❌ Error: HTTP {response.status_code}")
                
                print("   ✅ REST API demo completed!")
                print()
                
        except Exception as e:
            print(f"   ❌ REST API error: {e}")

async def main():
    """Main demo function"""
    demo = WebInterfaceDemo()
    await demo.run_demo()

if __name__ == "__main__":
    asyncio.run(main())
