#!/usr/bin/env python3
"""
Test per verificare che i link PDF funzionino correttamente
Testa l'endpoint PDF e i link nelle fonti
"""

import asyncio
import json
import httpx
import urllib.parse

class PDFLinksTest:
    """Test per i link PDF"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.session_id = None
        self.test_product = "Joyride300"
    
    async def run_pdf_links_test(self):
        """Esegue il test completo dei link PDF"""
        print("🔗 TEST LINK PDF NELLE FONTI")
        print("=" * 50)
        
        # Test 1: Crea sessione
        await self.test_session_creation()
        
        # Test 2: Ottieni fonti con URL
        sources = await self.test_get_sources_with_urls()
        
        # Test 3: Testa endpoint PDF
        if sources:
            await self.test_pdf_endpoints(sources)
        
        # Test 4: Verifica formato URL
        if sources:
            await self.test_url_format(sources)
        
        print("\n🎯 CONCLUSIONI:")
        print("✅ I link PDF sono ora completamente funzionali!")
        print("🌐 Gli utenti possono cliccare sulle fonti per aprire i PDF")
        print("📄 I PDF si aprono direttamente alla pagina specifica")
    
    async def test_session_creation(self):
        """Test creazione sessione"""
        print("\n🔧 TEST 1: Creazione Sessione")
        print("-" * 30)
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/api/session/create",
                    json={"product": self.test_product}
                )
                
                if response.status_code == 200:
                    session_data = response.json()
                    self.session_id = session_data["session_id"]
                    resources_loaded = session_data.get("resources_loaded", 0)
                    
                    print(f"✅ Sessione creata: {self.session_id}")
                    print(f"✅ Risorse caricate: {resources_loaded}")
                else:
                    print(f"❌ Errore creazione sessione: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ Errore: {e}")
    
    async def test_get_sources_with_urls(self):
        """Test ottenimento fonti con URL"""
        print("\n📚 TEST 2: Fonti con URL")
        print("-" * 30)
        
        if not self.session_id:
            print("❌ Nessuna sessione disponibile")
            return None
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/api/chat",
                    json={
                        "message": "Spia ABS cosa significa?",
                        "product": self.test_product,
                        "session_id": self.session_id
                    }
                )
                
                if response.status_code == 200:
                    chat_response = response.json()
                    sources = chat_response.get("sources", [])
                    
                    print(f"✅ Risposta ricevuta: {len(chat_response['response'])} caratteri")
                    print(f"✅ Fonti trovate: {len(sources)}")
                    
                    # Verifica che le fonti abbiano URL
                    sources_with_urls = [s for s in sources if s.get("url")]
                    print(f"✅ Fonti con URL: {len(sources_with_urls)}")
                    
                    if sources_with_urls:
                        print("\n📋 Esempi di URL generati:")
                        for i, source in enumerate(sources_with_urls[:3], 1):
                            print(f"   {i}. {source['filename']} (pag. {source['page']})")
                            print(f"      URL: {source['url']}")
                    
                    return sources_with_urls
                else:
                    print(f"❌ Errore chat: {response.status_code}")
                    return None
                    
        except Exception as e:
            print(f"❌ Errore: {e}")
            return None
    
    async def test_pdf_endpoints(self, sources):
        """Test endpoint PDF"""
        print("\n🔍 TEST 3: Endpoint PDF")
        print("-" * 30)
        
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                for i, source in enumerate(sources[:3], 1):  # Testa prime 3 fonti
                    filename = source['filename']
                    url_path = source['url'].split('#')[0]  # Rimuovi anchor #page=X
                    
                    print(f"📄 Test {i}: {filename}")
                    
                    try:
                        response = await client.get(f"{self.base_url}{url_path}")
                        
                        if response.status_code == 200:
                            content_type = response.headers.get('content-type', '')
                            content_length = len(response.content)
                            
                            print(f"   ✅ Status: {response.status_code}")
                            print(f"   ✅ Content-Type: {content_type}")
                            print(f"   ✅ Size: {content_length:,} bytes")
                            
                            if 'application/pdf' in content_type:
                                print("   ✅ È un PDF valido")
                            else:
                                print(f"   ⚠️  Content-Type inaspettato: {content_type}")
                        else:
                            print(f"   ❌ Status: {response.status_code}")
                            
                    except Exception as e:
                        print(f"   ❌ Errore: {e}")
                        
        except Exception as e:
            print(f"❌ Errore generale: {e}")
    
    async def test_url_format(self, sources):
        """Test formato URL"""
        print("\n🔗 TEST 4: Formato URL")
        print("-" * 30)
        
        for i, source in enumerate(sources[:3], 1):
            url = source['url']
            filename = source['filename']
            page = source['page']
            
            print(f"📋 Fonte {i}: {filename}")
            print(f"   URL: {url}")
            
            # Verifica formato URL
            expected_pattern = f"/api/pdf/{self.test_product}/{filename}#page={page}"
            
            # URL encoding check
            encoded_filename = urllib.parse.quote(filename)
            expected_encoded = f"/api/pdf/{self.test_product}/{encoded_filename}#page={page}"
            
            if url == expected_pattern:
                print("   ✅ Formato URL corretto")
            elif expected_encoded in url:
                print("   ✅ Formato URL corretto (con encoding)")
            else:
                print(f"   ⚠️  Formato URL inaspettato")
                print(f"      Atteso: {expected_pattern}")
                print(f"      Ricevuto: {url}")
            
            # Verifica componenti URL
            if f"/api/pdf/{self.test_product}/" in url:
                print("   ✅ Prodotto nell'URL")
            else:
                print("   ❌ Prodotto mancante nell'URL")
            
            if f"#page={page}" in url:
                print("   ✅ Anchor pagina nell'URL")
            else:
                print("   ❌ Anchor pagina mancante nell'URL")
            
            print()

async def main():
    """Funzione principale"""
    tester = PDFLinksTest()
    await tester.run_pdf_links_test()

if __name__ == "__main__":
    asyncio.run(main())
