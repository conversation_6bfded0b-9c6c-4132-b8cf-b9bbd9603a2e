#!/usr/bin/env python3
"""
Test per le nuove funzionalità visive del frontend
Testa indicatori di pensiero, formattazione markdown e gestione fonti
"""

import asyncio
import json
import time
import httpx
import websockets

class VisualFeaturesTest:
    """Test delle funzionalità visive avanzate"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session_id = None
    
    async def run_visual_tests(self):
        """Esegue i test delle funzionalità visive"""
        print("🎨 TEST FUNZIONALITÀ VISIVE AVANZATE")
        print("=" * 50)
        
        # Verifica server
        if not await self.check_server():
            print("❌ Server non disponibile")
            return
        
        print("✅ Server attivo")
        
        # Crea sessione
        await self.create_test_session()
        
        # Test WebSocket con indicatori di progresso
        await self.test_websocket_progress_indicators()
        
        # Test formattazione markdown
        await self.test_markdown_formatting()
        
        # Test gestione fonti
        await self.test_sources_handling()
        
        print("\n🎉 Test delle funzionalità visive completato!")
        print("👀 Controlla il browser per vedere gli effetti visivi")
    
    async def check_server(self):
        """Verifica se il server è attivo"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.base_url}/api/health")
                return response.status_code == 200
        except:
            return False
    
    async def create_test_session(self):
        """Crea una sessione di test"""
        try:
            async with httpx.AsyncClient() as client:
                # Ottieni prodotti
                products_response = await client.get(f"{self.base_url}/api/products")
                products = products_response.json()
                
                if not products:
                    print("❌ Nessun prodotto disponibile")
                    return
                
                # Crea sessione
                session_response = await client.post(
                    f"{self.base_url}/api/session/create",
                    json={"product": products[0]["name"]}
                )
                
                session_data = session_response.json()
                self.session_id = session_data["session_id"]
                print(f"✅ Sessione creata: {self.session_id}")
                
        except Exception as e:
            print(f"❌ Errore creazione sessione: {e}")
    
    async def test_websocket_progress_indicators(self):
        """Test degli indicatori di progresso WebSocket"""
        if not self.session_id:
            print("❌ Nessuna sessione per test WebSocket")
            return
        
        print("\n💭 Test Indicatori di Progresso WebSocket:")
        
        try:
            ws_url = f"ws://localhost:8000/ws/{self.session_id}"
            
            async with websockets.connect(ws_url) as websocket:
                print("   📡 WebSocket connesso")
                
                # Invia messaggio che dovrebbe generare indicatori di progresso
                test_message = {
                    "message": "Come si sostituisce l'olio motore nel Symphony ST 200?",
                    "product": "Symphony ST 200",
                    "session_id": self.session_id
                }
                
                print(f"   📤 Invio: {test_message['message']}")
                await websocket.send(json.dumps(test_message))
                
                # Monitora i messaggi di progresso
                progress_messages = []
                final_response = None
                
                timeout = 30  # 30 secondi timeout
                start_time = time.time()
                
                while time.time() - start_time < timeout:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        data = json.loads(response)
                        
                        if data.get("type") == "typing":
                            print(f"   ⏳ Typing indicator: {data.get('status', 'processing')}")
                        elif data.get("type") == "progress":
                            progress_msg = data.get("message", "")
                            progress_messages.append(progress_msg)
                            print(f"   🔄 Progress: {progress_msg}")
                        elif data.get("type") == "message":
                            final_response = data
                            print(f"   ✅ Risposta ricevuta: {len(data['response'])} caratteri")
                            if data.get("sources"):
                                print(f"   📚 Fonti trovate: {len(data['sources'])}")
                            break
                        elif data.get("type") == "error":
                            print(f"   ❌ Errore: {data.get('message', 'Unknown error')}")
                            break
                            
                    except asyncio.TimeoutError:
                        print("   ⏰ Timeout in attesa di risposta")
                        break
                
                print(f"   📊 Messaggi di progresso ricevuti: {len(progress_messages)}")
                
                if final_response:
                    print("   ✅ Test indicatori di progresso completato")
                else:
                    print("   ⚠️  Nessuna risposta finale ricevuta")
                
        except Exception as e:
            print(f"   ❌ Errore WebSocket: {e}")
    
    async def test_markdown_formatting(self):
        """Test della formattazione markdown"""
        print("\n📝 Test Formattazione Markdown:")
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Messaggio che dovrebbe generare risposta con markdown
                markdown_message = {
                    "message": "Elenca i passaggi per la manutenzione ordinaria",
                    "product": "Symphony ST 200",
                    "session_id": self.session_id
                }
                
                print(f"   📤 Invio: {markdown_message['message']}")
                
                response = await client.post(
                    f"{self.base_url}/api/chat",
                    json=markdown_message
                )
                
                if response.status_code == 200:
                    chat_response = response.json()
                    response_text = chat_response['response']
                    
                    # Verifica presenza di elementi markdown
                    has_bold = '**' in response_text or '__' in response_text
                    has_lists = '- ' in response_text or any(f'{i}. ' in response_text for i in range(1, 10))
                    has_newlines = '\n' in response_text
                    
                    print(f"   📄 Risposta: {len(response_text)} caratteri")
                    print(f"   🔤 Grassetto rilevato: {'✅' if has_bold else '❌'}")
                    print(f"   📋 Liste rilevate: {'✅' if has_lists else '❌'}")
                    print(f"   📐 A capo rilevati: {'✅' if has_newlines else '❌'}")
                    
                    if has_bold or has_lists or has_newlines:
                        print("   ✅ Formattazione markdown presente")
                    else:
                        print("   ⚠️  Nessuna formattazione markdown rilevata")
                else:
                    print(f"   ❌ Errore HTTP: {response.status_code}")
                    
        except Exception as e:
            print(f"   ❌ Errore test markdown: {e}")
    
    async def test_sources_handling(self):
        """Test della gestione delle fonti"""
        print("\n📚 Test Gestione Fonti:")
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Messaggio tecnico che dovrebbe generare fonti
                sources_message = {
                    "message": "Dove trovo le specifiche tecniche del motore?",
                    "product": "Symphony ST 200", 
                    "session_id": self.session_id
                }
                
                print(f"   📤 Invio: {sources_message['message']}")
                
                response = await client.post(
                    f"{self.base_url}/api/chat",
                    json=sources_message
                )
                
                if response.status_code == 200:
                    chat_response = response.json()
                    sources = chat_response.get('sources', [])
                    citations = chat_response.get('citations', [])
                    
                    print(f"   📄 Fonti trovate: {len(sources)}")
                    print(f"   📝 Citazioni trovate: {len(citations)}")
                    
                    if sources:
                        print("   📋 Dettagli fonti:")
                        for i, source in enumerate(sources[:3], 1):  # Mostra prime 3
                            filename = source.get('filename', 'Unknown')
                            page = source.get('page', 0)
                            print(f"      {i}. {filename} (pag. {page})")
                        
                        print("   ✅ Fonti disponibili per modal")
                    else:
                        print("   ⚠️  Nessuna fonte trovata")
                        
                    if citations:
                        print("   ✅ Citazioni disponibili")
                    else:
                        print("   ⚠️  Nessuna citazione trovata")
                        
                else:
                    print(f"   ❌ Errore HTTP: {response.status_code}")
                    
        except Exception as e:
            print(f"   ❌ Errore test fonti: {e}")

async def main():
    """Funzione principale"""
    tester = VisualFeaturesTest()
    await tester.run_visual_tests()

if __name__ == "__main__":
    asyncio.run(main())
