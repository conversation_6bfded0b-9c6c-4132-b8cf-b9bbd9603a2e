#!/usr/bin/env python3
"""
Complete system test for both dynamic whitelist and diagnostic codes
"""

import asyncio
import logging
from security_utils import domain_validator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_complete_system():
    """Test the complete improved system"""
    print("=== Complete System Test ===")
    print("Testing both dynamic whitelist and diagnostic codes functionality")
    
    # Get current stats
    stats = domain_validator.get_whitelist_stats()
    print(f"Current whitelist size: {stats['whitelist_size']}")
    
    # Comprehensive test cases
    test_queries = [
        # === DYNAMIC WHITELIST TESTS ===
        # Brand names from documents
        ("Lubrificanti Silkolene", True, "Brand name from documents"),
        ("sistema Synerject", True, "Technical system from documents"),
        ("Silkolene PRO 4", True, "Specific product from documents"),
        ("corpo farfallato", True, "Technical term from documents"),
        ("Symphony ST200", True, "Model name from documents"),
        
        # === DIAGNOSTIC CODES TESTS ===
        # Basic diagnostic patterns
        ("codice guasto P0116", True, "Temperature sensor diagnostic"),
        ("P0420", True, "Catalyst diagnostic code"),
        ("codice errore P0108", True, "MAP sensor diagnostic"),
        ("codici guasto", True, "General diagnostic term"),
        ("errore P0136", True, "Oxygen sensor diagnostic"),
        
        # OBD-II codes from documents
        ("P0106", True, "MAP sensor from documents"),
        ("P0261", True, "Injector code from documents"),
        ("C1032", True, "ABS code from documents"),
        
        # === BASIC AUTOMOTIVE TERMS ===
        ("olio motore", True, "Basic automotive term"),
        ("filtro aria", True, "Basic automotive term"),
        ("cambio olio", True, "Basic maintenance term"),
        ("freno anteriore", True, "Basic automotive term"),
        
        # === TECHNICAL SPECIFICATIONS ===
        ("euro 5 specifiche", True, "Standard specification"),
        ("10W-40 olio", True, "Oil specification"),
        ("SAE 5W-40", True, "Oil grade specification"),
        
        # === NON-AUTOMOTIVE (SHOULD BE BLOCKED) ===
        ("come cucinare la pizza", False, "Cooking - should be blocked"),
        ("programmazione python", False, "Programming - should be blocked"),
        ("codice sorgente", False, "Source code - should be blocked"),
        ("previsioni del tempo", False, "Weather - should be blocked"),
        ("film al cinema", False, "Entertainment - should be blocked"),
        
        # === EDGE CASES ===
        ("codice", True, "Just 'codice' in automotive context"),
        ("guasto", True, "Just 'guasto' should be allowed"),
        ("sistema", True, "Just 'sistema' should be allowed"),
        ("M4B ricalibrazione", True, "Technical procedure from documents"),
    ]
    
    print(f"\nTesting {len(test_queries)} queries across all categories...")
    
    # Track results by category
    categories = {
        "Dynamic Whitelist": 0,
        "Diagnostic Codes": 0, 
        "Basic Automotive": 0,
        "Technical Specs": 0,
        "Non-Automotive": 0,
        "Edge Cases": 0
    }
    
    category_totals = {
        "Dynamic Whitelist": 5,
        "Diagnostic Codes": 8,
        "Basic Automotive": 4,
        "Technical Specs": 3,
        "Non-Automotive": 5,
        "Edge Cases": 4
    }
    
    passed = 0
    total = len(test_queries)
    current_category = "Dynamic Whitelist"
    category_index = 0
    
    category_names = list(categories.keys())
    category_limits = [5, 8, 4, 3, 5, 4]  # Number of tests per category
    
    for i, (query, expected_allowed, description) in enumerate(test_queries):
        # Determine current category
        if i >= sum(category_limits[:category_index+1]):
            category_index += 1
            current_category = category_names[category_index]
        
        is_relevant, error = domain_validator.validate_domain_relevance(query)
        
        if is_relevant == expected_allowed:
            status = "✓ PASS"
            passed += 1
            categories[current_category] += 1
        else:
            status = "✗ FAIL"
        
        allowed_str = "ALLOWED" if is_relevant else "BLOCKED"
        expected_str = "should be ALLOWED" if expected_allowed else "should be BLOCKED"
        
        print(f"{status}: '{query}' -> {allowed_str} ({expected_str}) - {description}")
        if error and not is_relevant:
            print(f"      Reason: {error}")
    
    print(f"\n=== Overall Results ===")
    print(f"Total Passed: {passed}/{total} ({passed/total*100:.1f}%)")
    
    print(f"\n=== Results by Category ===")
    all_passed = True
    for category, passed_count in categories.items():
        total_count = category_totals[category]
        percentage = (passed_count / total_count) * 100
        status = "✅" if passed_count == total_count else "⚠️"
        print(f"{status} {category}: {passed_count}/{total_count} ({percentage:.1f}%)")
        if passed_count != total_count:
            all_passed = False
    
    print(f"\n=== Final Assessment ===")
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Dynamic whitelist system is working perfectly")
        print("✅ Diagnostic codes system is working perfectly")
        print("✅ Basic automotive terms are working perfectly")
        print("✅ Non-automotive filtering is working perfectly")
    else:
        print("⚠️  Some tests failed - system may need adjustments")
    
    # Show sample terms from whitelist
    print(f"\n=== Sample Whitelist Terms ===")
    brand_terms = [t for t in domain_validator.document_whitelist if any(b in t for b in ['silkolene', 'synerject', 'symphony'])][:5]
    diagnostic_terms = [t for t in domain_validator.document_whitelist if t.startswith(('p0', 'c1', 'b0', 'u0'))][:5]
    
    print("Brand/Product terms:")
    for term in brand_terms:
        print(f"  - {term}")
    
    print("Diagnostic codes:")
    for term in diagnostic_terms:
        print(f"  - {term}")

if __name__ == "__main__":
    asyncio.run(test_complete_system())
