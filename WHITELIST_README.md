# Sistema di Whitelist Dinamica per Termini Automotive

## Panoramica

Il sistema di whitelist dinamica è stato implementato per risolvere il problema dei falsi negativi nel riconoscimento di termini automotive validi. Il sistema estrae automaticamente termini tecnici dai documenti PDF presenti nella directory `sorgenti/` e li utilizza per validare le query degli utenti.

## Problemi Risolti

### 1. Termini Brand/Prodotto Bloccati
**Prima**: Termini come "Lubrificanti Silkolene" venivano bloccati perché non erano nella lista hardcoded delle parole chiave automotive, anche se erano presenti nella documentazione tecnica.

**Dopo**: Il sistema estrae automaticamente questi termini dai documenti e li utilizza per validare le query, permettendo l'accesso a informazioni tecniche specifiche presenti nella documentazione.

### 2. Codici Diagnostici Bloccati
**Prima**: Query come "codice guasto P0116" venivano bloccate perché "codice" era erroneamente nella lista delle parole non-automotive.

**Dopo**: <PERSON><PERSON><PERSON> "codice" dalle esclusioni e aggiunti pattern specifici per codici diagnostici (P0xxx, C1xxx, ecc.), permettendo diagnosi complete dei guasti.

## Come Funziona

### 1. Estrazione Automatica dei Termini

Il sistema analizza tutti i file PDF nella directory `sorgenti/` ed estrae:

- **Nomi di brand**: Silkolene, Castrol, Motul, Synerject, ecc.
- **Termini tecnici composti**: "corpo farfallato", "sistema Synerject", "lubrificanti Silkolene"
- **Codici modello**: ST200, M4B, E5+, ecc.
- **Specifiche tecniche**: misurazioni, standard Euro, ecc.

### 2. Pattern di Estrazione

Il sistema utilizza diversi pattern regex per identificare termini automotive:

```python
# Brand names
r'\b[a-z]*silkolene[a-z]*\b'
r'\b[a-z]*synerject[a-z]*\b'

# Technical compound terms
r'\b(?:lubrificant[ei]|olio|grasso)\s+[a-z]+\b'
r'\b(?:sistema|sistemi|impianto)\s+[a-z]+\b'

# Model codes
r'\b[a-z]+\s*\d+[a-z]*\b'

# Diagnostic codes
r'\b(codice|codici)\s+(guasto|errore|diagnostico)\b'
r'\bp\d{4}\b'  # OBD-II codes like P0116
r'\b[a-z]\d{4}\b'  # Generic diagnostic codes
```

### 3. Cache e Aggiornamenti

- I termini estratti vengono salvati in `document_whitelist_cache.json`
- La whitelist viene aggiornata automaticamente ogni 24 ore
- È possibile forzare un aggiornamento manuale

## Utilizzo

### Aggiornamento Automatico

Il sistema si aggiorna automaticamente quando viene processata una query:

```python
# Nel query_engine.py
await domain_validator.update_document_whitelist()
```

### Aggiornamento Manuale

Utilizzare lo script di utilità:

```bash
# Mostra statistiche
python update_whitelist.py stats

# Aggiornamento normale (rispetta l'intervallo di 24h)
python update_whitelist.py update

# Forza aggiornamento immediato
python update_whitelist.py force-update

# Testa una query specifica
python update_whitelist.py test --query "Lubrificanti Silkolene"
```

### Test del Sistema

Eseguire il test completo:

```bash
python test_final_whitelist.py
```

## Statistiche Attuali

- **Termini nella whitelist**: ~2400+
- **Documenti analizzati**: 16 PDF
- **Aggiornamento**: Ogni 24 ore
- **Cache**: `document_whitelist_cache.json`

## Esempi di Termini Estratti

### Brand e Prodotti
- `silkolene`
- `lubrificanti silkolene`
- `synerject`
- `castrol`
- `motul`

### Termini Tecnici
- `corpo farfallato`
- `sistema synerject`
- `symphony 200`
- `m4b`
- `euro 5`

### Codici e Specifiche
- `st200`
- `e5+`
- `10w-40`
- `sae 5w-40`

### Codici Diagnostici
- `p0116` (Sensore temperatura liquido raffreddamento)
- `p0420` (Catalizzatore)
- `p0108` (Sensore MAP)
- `c1032` (Sistema ABS)
- `p0261` (Iniettore)

## Validazione delle Query

Il sistema valida le query in questo ordine:

1. **Controllo esclusioni**: Verifica se contiene termini non-automotive (cucina, programmazione, ecc.)
2. **Parole chiave statiche**: Controlla la lista hardcoded di termini automotive
3. **Whitelist dinamica**: Controlla i termini estratti dai documenti
4. **Pattern automotive**: Utilizza regex per identificare pattern tipici automotive

## Configurazione

### Intervallo di Aggiornamento

```python
# In security_utils.py
self.whitelist_update_interval = timedelta(hours=24)
```

### File di Cache

```python
self.whitelist_cache_file = Path("document_whitelist_cache.json")
```

## Monitoraggio

### Log di Sistema

Il sistema logga le seguenti informazioni:

```
INFO - Updated whitelist: 2363 -> 2437 terms (+74)
INFO - Query approved by document whitelist: silkolene
INFO - Found 16 PDF files to analyze
```

### Metriche

- Dimensione whitelist
- Ultimo aggiornamento
- Termini aggiunti per aggiornamento
- Query approvate dalla whitelist

## Manutenzione

### Pulizia Cache

Per resettare la cache:

```bash
rm document_whitelist_cache.json
python update_whitelist.py force-update
```

### Aggiunta Nuovi Documenti

Quando vengono aggiunti nuovi PDF alla directory `sorgenti/`:

1. Il sistema li rileverà automaticamente al prossimo aggiornamento
2. Per aggiornamento immediato: `python update_whitelist.py force-update`

## Benefici

1. **Riduzione falsi negativi**: Termini presenti nei documenti non vengono più bloccati
2. **Aggiornamento automatico**: La whitelist si mantiene sincronizzata con i documenti
3. **Flessibilità**: Supporta nuovi brand e termini tecnici automaticamente
4. **Trasparenza**: Log dettagliati mostrano quando e perché una query viene approvata
5. **Performance**: Cache efficiente riduce il tempo di elaborazione
