#!/usr/bin/env python3
"""
Integration test for the web interface
Tests the complete frontend-backend integration
"""

import asyncio
import json
import logging
from pathlib import Path
import httpx
import websockets
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebIntegrationTest:
    """Test suite for web interface integration"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session_id = None
        self.test_results = []
    
    async def run_all_tests(self):
        """Run all integration tests"""
        logger.info("🧪 Starting Web Integration Tests...")
        
        tests = [
            ("Health Check", self.test_health_check),
            ("Products API", self.test_products_api),
            ("Session Creation", self.test_session_creation),
            ("WebSocket Connection", self.test_websocket_connection),
            ("Chat Message", self.test_chat_message),
            ("Static Files", self.test_static_files),
        ]
        
        for test_name, test_func in tests:
            try:
                logger.info(f"Running test: {test_name}")
                result = await test_func()
                self.test_results.append((test_name, "PASS", result))
                logger.info(f"✅ {test_name}: PASS")
            except Exception as e:
                self.test_results.append((test_name, "FAIL", str(e)))
                logger.error(f"❌ {test_name}: FAIL - {e}")
        
        self.print_summary()
    
    async def test_health_check(self):
        """Test health check endpoint"""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/api/health")
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert data["mcp_server"] is True
            assert data["session_manager"] is True
            return f"Server healthy with {data.get('cache', {}).get('entries', 0)} cache entries"
    
    async def test_products_api(self):
        """Test products API endpoint"""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/api/products")
            assert response.status_code == 200
            products = response.json()
            assert isinstance(products, list)
            assert len(products) > 0
            
            # Verify product structure
            for product in products:
                assert "name" in product
                assert "display_name" in product
                assert "available" in product
            
            return f"Found {len(products)} products: {[p['name'] for p in products]}"
    
    async def test_session_creation(self):
        """Test session creation"""
        # First get available products
        async with httpx.AsyncClient() as client:
            products_response = await client.get(f"{self.base_url}/api/products")
            products = products_response.json()
            
            if not products:
                raise Exception("No products available for testing")
            
            # Create session with first product
            test_product = products[0]["name"]
            session_response = await client.post(
                f"{self.base_url}/api/session/create",
                json={"product": test_product}
            )
            
            assert session_response.status_code == 200
            session_data = session_response.json()
            assert "session_id" in session_data
            assert session_data["product"] == test_product
            
            self.session_id = session_data["session_id"]
            return f"Created session {self.session_id} for product {test_product}"
    
    async def test_websocket_connection(self):
        """Test WebSocket connection"""
        if not self.session_id:
            raise Exception("No session ID available for WebSocket test")
        
        ws_url = f"ws://localhost:8000/ws/{self.session_id}"
        
        try:
            async with websockets.connect(ws_url) as websocket:
                # Send a test message
                test_message = {
                    "message": "Test message",
                    "product": "Symphony ST 200",
                    "session_id": self.session_id
                }
                
                await websocket.send(json.dumps(test_message))
                
                # Wait for response (with timeout)
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                response_data = json.loads(response)
                
                # Should receive typing indicator first
                if response_data.get("type") == "typing":
                    # Wait for actual response
                    response = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                    response_data = json.loads(response)
                
                assert response_data.get("type") == "message"
                assert "response" in response_data
                
                return f"WebSocket communication successful, received response: {response_data['response'][:50]}..."
                
        except asyncio.TimeoutError:
            raise Exception("WebSocket response timeout")
    
    async def test_chat_message(self):
        """Test REST chat endpoint"""
        async with httpx.AsyncClient(timeout=30.0) as client:
            chat_data = {
                "message": "Come posso sostituire l'olio del motore?",
                "product": "Symphony ST 200",
                "session_id": self.session_id
            }
            
            response = await client.post(
                f"{self.base_url}/api/chat",
                json=chat_data
            )
            
            assert response.status_code == 200
            chat_response = response.json()
            assert "response" in chat_response
            assert "citations" in chat_response
            assert "timestamp" in chat_response
            
            return f"Chat response received: {chat_response['response'][:50]}..."
    
    async def test_static_files(self):
        """Test static file serving"""
        static_files = [
            "/static/style.css",
            "/static/script.js"
        ]
        
        results = []
        async with httpx.AsyncClient() as client:
            for file_path in static_files:
                response = await client.get(f"{self.base_url}{file_path}")
                assert response.status_code == 200
                results.append(f"{file_path}: {len(response.content)} bytes")
        
        return f"Static files served: {', '.join(results)}"
    
    def print_summary(self):
        """Print test summary"""
        logger.info("\n" + "="*60)
        logger.info("🧪 WEB INTEGRATION TEST SUMMARY")
        logger.info("="*60)
        
        passed = sum(1 for _, status, _ in self.test_results if status == "PASS")
        failed = sum(1 for _, status, _ in self.test_results if status == "FAIL")
        
        for test_name, status, result in self.test_results:
            status_icon = "✅" if status == "PASS" else "❌"
            logger.info(f"{status_icon} {test_name}: {status}")
            if result:
                logger.info(f"   └─ {result}")
        
        logger.info("-"*60)
        logger.info(f"📊 Results: {passed} passed, {failed} failed")
        
        if failed == 0:
            logger.info("🎉 All tests passed! Web interface is working correctly.")
        else:
            logger.error(f"⚠️  {failed} test(s) failed. Please check the issues above.")

async def main():
    """Main test function"""
    tester = WebIntegrationTest()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
