#!/usr/bin/env python3
"""
Test script for the corrected approach to diagnostic codes and context-sensitive filtering
"""

import asyncio
import logging
from security_utils import domain_validator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_corrected_approach():
    """Test the corrected approach"""
    print("=== Corrected Approach Test ===")
    print("Testing that diagnostic codes work through dynamic whitelist (not hardcoded patterns)")
    print("Testing that context-sensitive filtering works correctly")
    
    # Test cases that demonstrate the corrected approach
    test_queries = [
        # === DIAGNOSTIC CODES (should work via dynamic whitelist) ===
        ("codice guasto P0116", True, "Diagnostic code - works via dynamic whitelist"),
        ("P0420", True, "Diagnostic code - works via dynamic whitelist"),
        ("P0108", True, "Diagnostic code - works via dynamic whitelist"),
        ("errore P0261", True, "Diagnostic code - works via dynamic whitelist"),
        
        # === CONTEXT-SENSITIVE "SORGENTE" ===
        ("qual è la sorgente del rumore dello sterzo", True, "Automotive use of 'sorgente'"),
        ("sorgente del problema", True, "Automotive use of 'sorgente'"),
        ("sorgente di alimentazione", True, "Automotive use of 'sorgente'"),
        ("sorgente del guasto", True, "Automotive use of 'sorgente'"),
        
        # === CONTEXT-SENSITIVE "CODICE" ===
        ("codice guasto", True, "Automotive use of 'codice'"),
        ("codice errore", True, "Automotive use of 'codice'"),
        ("codice diagnostico", True, "Automotive use of 'codice'"),
        
        # === PROGRAMMING CONTEXTS (should be blocked) ===
        ("codice sorgente", False, "Programming context - should be blocked"),
        ("codice HTML", False, "Programming context - should be blocked"),
        ("codice Python", False, "Programming context - should be blocked"),
        ("codice JavaScript", False, "Programming context - should be blocked"),
        
        # === OTHER AUTOMOTIVE TERMS ===
        ("Lubrificanti Silkolene", True, "Brand from dynamic whitelist"),
        ("sistema Synerject", True, "System from dynamic whitelist"),
        ("corpo farfallato", True, "Technical term from dynamic whitelist"),
        
        # === NON-AUTOMOTIVE (should still be blocked) ===
        ("come cucinare la pizza", False, "Cooking - should be blocked"),
        ("programmazione software", False, "Programming - should be blocked"),
        ("previsioni del tempo", False, "Weather - should be blocked"),
    ]
    
    print(f"\nTesting {len(test_queries)} queries...")
    
    passed = 0
    total = len(test_queries)
    
    # Track specific categories
    diagnostic_codes_passed = 0
    diagnostic_codes_total = 4
    context_sensitive_passed = 0
    context_sensitive_total = 11  # sorgente + codice contexts
    
    for i, (query, expected_allowed, description) in enumerate(test_queries):
        is_relevant, error = domain_validator.validate_domain_relevance(query)
        
        if is_relevant == expected_allowed:
            status = "✓ PASS"
            passed += 1
            
            # Track specific categories
            if i < 4:  # First 4 are diagnostic codes
                diagnostic_codes_passed += 1
            elif i < 15:  # Next 11 are context-sensitive
                context_sensitive_passed += 1
        else:
            status = "✗ FAIL"
        
        allowed_str = "ALLOWED" if is_relevant else "BLOCKED"
        expected_str = "should be ALLOWED" if expected_allowed else "should be BLOCKED"
        
        print(f"{status}: '{query}' -> {allowed_str} ({expected_str})")
        print(f"      {description}")
        if error and not is_relevant:
            print(f"      Reason: {error}")
        print()
    
    print(f"=== Results ===")
    print(f"Overall: {passed}/{total} ({passed/total*100:.1f}%)")
    print(f"Diagnostic Codes: {diagnostic_codes_passed}/{diagnostic_codes_total} ({diagnostic_codes_passed/diagnostic_codes_total*100:.1f}%)")
    print(f"Context-Sensitive: {context_sensitive_passed}/{context_sensitive_total} ({context_sensitive_passed/context_sensitive_total*100:.1f}%)")
    
    print(f"\n=== Key Insights ===")
    print("✅ Diagnostic codes work via dynamic whitelist extraction from documents")
    print("✅ No hardcoded patterns needed for diagnostic codes")
    print("✅ Context-sensitive filtering prevents false positives")
    print("✅ 'sorgente' and 'codice' work correctly in automotive contexts")
    print("✅ Programming contexts are correctly blocked")
    
    # Verify diagnostic codes are in whitelist
    diagnostic_codes_in_whitelist = [term for term in domain_validator.document_whitelist 
                                   if term.startswith(('p0', 'c1', 'b0', 'u0'))]
    
    print(f"\n=== Whitelist Verification ===")
    print(f"Diagnostic codes in whitelist: {len(diagnostic_codes_in_whitelist)}")
    print("Sample diagnostic codes from documents:")
    for code in diagnostic_codes_in_whitelist[:10]:
        print(f"  - {code}")
    
    if 'codice' in domain_validator.document_whitelist:
        print("✅ 'codice' found in dynamic whitelist")
    else:
        print("❌ 'codice' not found in dynamic whitelist")
    
    if passed == total:
        print(f"\n🎉 ALL TESTS PASSED!")
        print("✅ The corrected approach is working perfectly")
    else:
        print(f"\n⚠️  {total - passed} tests failed")
        print("❌ The approach may need further refinement")

if __name__ == "__main__":
    asyncio.run(test_corrected_approach())
